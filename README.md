# House Agreement Generator - Django Application

A Django web application for generating professional house rental agreement PDFs and Word documents with multiple receipts per page functionality.

## Features

### Core Functionality
- **Multi-format Generation**: Create both PDF and Word document formats
- **Multiple Receipts**: Generate 1-4 receipts per page as needed
- **Auto-fill Tenant Data**: Pre-populated tenant information for flats and shops
- **Real-time Calculations**: Automatic total amount calculation
- **Professional Formatting**: Exact table structure matching legal requirements

### Property Management
- **Fixed Property Details**: 
  - Holding No: 01663
  - Owner: <PERSON>d <PERSON>  
  - Location: Dangisara, Badalgachhi
- **Property Types**: House, Shop, Flat with pre-configured tenant data
- **Financial Tracking**: Monthly rent, electricity, water, and gas bills

### Generation Options
- **Receipts Per Page**: Choose 1, 2, 3, or 4 receipts per page
- **File Formats**: PDF or Word document (DOCX) output
- **Instant Download**: Direct file download after generation

## Technical Stack
- **Backend**: Django 5.2.4 with Python 3.13
- **PDF Generation**: ReportLab with custom table formatting
- **Word Documents**: python-docx with table structures
- **Frontend**: Bootstrap 5 with responsive design
- **Database**: SQLite for development

## Pre-configured Tenant Data

### Flats (₹25,000/month)
- **3A**: Md. Akhlas Ur Rahman (Father: Md. Abu Masud)
- **3B**: SAYED (Father: Shahidul)
- **4A**: MD. MEHEDI HASAN (Father: Md. Nasir Uddin)
- **4B**: MD. NAZMUS SAKIB SAYCOT (Father: Md. Sadekul Islam)

### Shops (₹35,000/month)
- **01**: Md. Jahangir Kabir (Father: Md. Afsar Ali Mandal)
- **02**: Shamol Kumar Mondal (Father: Nogendra Nath Mondal)

## Usage

1. **Start Server**: `python manage.py runserver`
2. **Create Receipt**: Visit `/create/` to fill out the form
3. **Select Options**: Choose receipts per page (1-4) and format (PDF/DOCX)
4. **Auto-fill**: Select property type and unit for automatic data population
5. **Generate**: Submit form to instantly download the document

## File Structure
```
agreements/
├── models.py          # HouseAgreement data model
├── forms.py           # Django forms with generation options
├── views.py           # Request handling and file generation
├── pdf_generator.py   # ReportLab PDF creation with multiple receipts
├── word_generator.py  # python-docx Word document generation
├── urls.py            # URL routing
└── templates/         # HTML templates with Bootstrap styling
```

## Development Notes
- PDF files maintain exact table structure from original format
- Word documents replicate the same layout with proper cell merging
- Multiple receipts are generated on single pages with proper spacing
- Form validation ensures all required fields are completed
- Responsive design works on desktop and mobile devices