from reportlab.lib.pagesizes import letter, A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from reportlab.lib.colors import black, red, blue, white
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from django.conf import settings
import os
from datetime import datetime

class HouseAgreementPDFGenerator:
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=16,  # Reduced from 20 to save space
            spaceAfter=3,  # Reduced from 5
            alignment=TA_CENTER,
            textColor=black,
            fontName='Helvetica-Bold'
        )
        
        # Add compact title style for multiple receipts
        self.compact_title_style = ParagraphStyle(
            'CompactTitle',
            parent=self.styles['Heading1'],
            fontSize=14,  # Even smaller for multiple receipts
            spaceAfter=2,
            alignment=TA_CENTER,
            textColor=black,
            fontName='Helvetica-Bold'
        )
    
    def create_single_receipt_table(self, agreement_data, compact=False):
        """Create a single receipt table structure"""
        form_data = [
            # Row 1: No. and Date
            ["No.", "", "", "Date:", f"{agreement_data.agreement_date.strftime('%d/%m/%Y')}", "", ""],
            
            # Row 2: House/Shop/Flat and Holding No.
            ["House/Shop/Flat:", agreement_data.property_type, "", "Holding No.", agreement_data.holding_no, "", ""],
            
            # Row 3: Owned by (spans across most columns)
            ["Owned by:", agreement_data.owner_name, "", "", "", "", ""],
            
            # Row 4: Village/Mahalla (spans across most columns)
            ["Village/Mahalla:", agreement_data.village_area, "", "", "", "", ""],
            
            # Row 5: Tenant Name (spans across most columns)
            ["Tenant Name:", agreement_data.tenant_name, "", "", "", "", ""],
            
            # Row 6: Father's Name (spans across most columns)
            ["Father's Name:", agreement_data.father_name, "", "", "", "", ""],
            
            # Row 7: Monthly Rent with "Money" (no duration field as requested)
            ["", "", "Monthly rent:", f"{agreement_data.monthly_rent:.2f}", "Money", "", ""],
            
            # Row 8: Bills in one row with proper structure (removed "as")
            ["Electricity Bill:", f"{agreement_data.electricity_bill:.2f}", "Water bill:", f"{agreement_data.water_bill:.2f}", "Gas bill:", f"{agreement_data.gas_bill:.2f}", ""],
            
            # Row 9: Total bill with "received"
            ["", "", "Total bill:", f"{agreement_data.total_amount:.2f}", "received", "", ""],
        ]
        
        # Adjust column widths and table size based on compact mode
        if compact:
            col_widths = [0.9*inch, 1.1*inch, 0.9*inch, 0.9*inch, 0.7*inch, 0.5*inch, 0.3*inch]
            font_size = 8
            padding = 3
        else:
            col_widths = [1.0*inch, 1.3*inch, 1.0*inch, 1.0*inch, 0.8*inch, 0.6*inch, 0.3*inch]
            font_size = 9
            padding = 6
        
        # Create table with exact column structure matching the image
        table = Table(form_data, colWidths=col_widths)
        
        # Table styling to exactly match the image
        table.setStyle(TableStyle([
            # Basic table styling
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), font_size),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, black),
            ('LEFTPADDING', (0, 0), (-1, -1), 2),
            ('RIGHTPADDING', (0, 0), (-1, -1), 2),
            ('TOPPADDING', (0, 0), (-1, -1), padding),
            ('BOTTOMPADDING', (0, 0), (-1, -1), padding),
            
            # Cell spanning to match the image structure
            ('SPAN', (1, 0), (2, 0)),  # No. field spans 3 columns
            ('SPAN', (1, 2), (6, 2)),  # Owned by spans columns 2-6
            ('SPAN', (1, 3), (6, 3)),  # Village/Mahalla spans columns 2-6  
            ('SPAN', (1, 4), (6, 4)),  # Tenant Name spans columns 2-6
            ('SPAN', (1, 5), (6, 5)),  # Father's Name spans columns 2-6
            ('SPAN', (0, 6), (1, 6)),  # Empty cells before Monthly rent
            ('SPAN', (0, 8), (1, 8)),  # Empty cells before Total
        ]))
        
        return table

    def generate_pdf(self, agreement_data, receipts_per_page=1, filename=None):
        """Generate a rent receipt PDF following the exact image structure"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"rent_receipt_{agreement_data.tenant_name.replace(' ', '_')}_{timestamp}.pdf"
        
        # Ensure media directory exists
        media_dir = settings.MEDIA_ROOT
        os.makedirs(media_dir, exist_ok=True)
        
        filepath = os.path.join(media_dir, filename)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4, 
                              leftMargin=0.8*inch, rightMargin=0.8*inch,
                              topMargin=0.8*inch, bottomMargin=0.8*inch)
        story = []
        
        # Generate the specified number of receipts
        for i in range(int(receipts_per_page)):
            if i > 0:
                story.append(Spacer(1, 40))  # Add space between receipts
            
            # Title exactly as shown in image
            title = Paragraph("Rent Receipt", self.title_style)
            story.append(title)
            story.append(Spacer(1, 10))
            
            # Red header bar exactly as shown in image
            red_header_table = Table([["part of the owner"]], colWidths=[6*inch])
            red_header_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, 0), red),
                ('TEXTCOLOR', (0, 0), (0, 0), white),
                ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (0, 0), 11),
                ('ALIGN', (0, 0), (0, 0), 'CENTER'),
                ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
                ('TOPPADDING', (0, 0), (0, 0), 6),
                ('BOTTOMPADDING', (0, 0), (0, 0), 6),
            ]))
            
            story.append(red_header_table)
            story.append(Spacer(1, 15))
            
            # Add the receipt table
            receipt_table = self.create_single_receipt_table(agreement_data)
            story.append(receipt_table)
            story.append(Spacer(1, 20))
            
            # Signature section exactly as shown in image
            signature_text = Paragraph('<para align="left" textColor="red" fontSize="10"><b>Signature of the tenant or person on his behalf</b></para>', self.styles['Normal'])
            story.append(signature_text)
            
            if i < int(receipts_per_page) - 1:  # Don't add page break after last receipt
                story.append(Spacer(1, 40))
        
        # Generate PDF
        doc.build(story)
        
        return filename, filepath

    def generate_multiple_receipts_pdf(self, receipt_data_list, filename=None):
        """Generate a PDF with multiple receipts - 2 per row, 4 per page maximum"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"multiple_receipts_{timestamp}.pdf"
        
        # Ensure media directory exists
        media_dir = settings.MEDIA_ROOT
        os.makedirs(media_dir, exist_ok=True)
        
        filepath = os.path.join(media_dir, filename)
        
        # Use tighter margins for multiple receipts
        doc = SimpleDocTemplate(filepath, pagesize=A4, 
                              leftMargin=0.4*inch, rightMargin=0.4*inch,
                              topMargin=0.5*inch, bottomMargin=0.5*inch)
        story = []
        
        # Process receipts in groups of 4 (2 rows of 2 receipts each)
        for page_start in range(0, len(receipt_data_list), 4):
            page_receipts = receipt_data_list[page_start:page_start + 4]
            
            # Add page break if this is not the first page
            if page_start > 0:
                from reportlab.platypus import PageBreak
                story.append(PageBreak())
            
            # Process receipts in pairs for each row
            for row_start in range(0, len(page_receipts), 2):
                row_receipts = page_receipts[row_start:row_start + 2]
                
                # Create horizontal layout for this row
                if len(row_receipts) == 2:
                    # Two receipts side by side
                    row_content = self._create_receipt_row(row_receipts[0], row_receipts[1])
                else:
                    # Single receipt (last one if odd number)
                    row_content = self._create_receipt_row(row_receipts[0], None)
                
                story.extend(row_content)
                
                # Add space between rows (but not after the last row on page)
                if row_start + 2 < len(page_receipts):
                    story.append(Spacer(1, 0.3*inch))
        
        # Generate PDF
        doc.build(story)
        return filename, filepath
    
    def _create_receipt_row(self, receipt1_data, receipt2_data=None):
        """Create a row with 1 or 2 receipts side by side"""
        row_content = []
        
        if receipt2_data is None:
            # Single receipt - use full width
            receipt_content = self._create_compact_receipt(receipt1_data, full_width=True)
            row_content.extend(receipt_content)
        else:
            # Two receipts side by side using a table layout
            left_receipt = self._create_compact_receipt_elements(receipt1_data)
            right_receipt = self._create_compact_receipt_elements(receipt2_data)
            
            # Create a table with two columns for side-by-side layout
            side_by_side_data = []
            max_elements = max(len(left_receipt), len(right_receipt))
            
            for i in range(max_elements):
                left_element = left_receipt[i] if i < len(left_receipt) else ""
                right_element = right_receipt[i] if i < len(right_receipt) else ""
                side_by_side_data.append([left_element, right_element])
            
            side_by_side_table = Table(side_by_side_data, colWidths=[3.6*inch, 3.6*inch])
            side_by_side_table.setStyle(TableStyle([
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 2),
                ('RIGHTPADDING', (0, 0), (-1, -1), 2),
                ('TOPPADDING', (0, 0), (-1, -1), 0),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
            ]))
            
            row_content.append(side_by_side_table)
        
        return row_content
    
    def _create_compact_receipt_elements(self, agreement_data):
        """Create individual receipt elements for side-by-side layout"""
        elements = []
        
        # Title
        title = Paragraph("Rent Receipt", ParagraphStyle(
            'CompactTitle',
            parent=self.styles['Heading1'],
            fontSize=12,
            spaceAfter=2,
            alignment=TA_CENTER,
            textColor=black,
            fontName='Helvetica-Bold'
        ))
        elements.append(title)
        elements.append(Spacer(1, 4))
        
        # Red header bar
        red_header_table = Table([["part of the owner"]], colWidths=[3.4*inch])
        red_header_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, 0), red),
            ('TEXTCOLOR', (0, 0), (0, 0), white),
            ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (0, 0), 9),
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),
            ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (0, 0), 3),
            ('BOTTOMPADDING', (0, 0), (0, 0), 3),
        ]))
        elements.append(red_header_table)
        elements.append(Spacer(1, 6))
        
        # Receipt table with compact styling
        receipt_table = self.create_compact_receipt_table(agreement_data)
        elements.append(receipt_table)
        elements.append(Spacer(1, 8))
        
        # Signature section
        signature_text = Paragraph('<para align="left" textColor="red" fontSize="8"><b>Signature of the tenant or person on his behalf</b></para>', 
                                 self.styles['Normal'])
        elements.append(signature_text)
        
        return elements
    
    def _create_compact_receipt(self, agreement_data, full_width=False):
        """Create a complete compact receipt"""
        elements = []
        width_multiplier = 7.2 if full_width else 3.4
        
        # Title
        title = Paragraph("Rent Receipt", ParagraphStyle(
            'CompactTitle',
            parent=self.styles['Heading1'],
            fontSize=14 if full_width else 12,
            spaceAfter=3,
            alignment=TA_CENTER,
            textColor=black,
            fontName='Helvetica-Bold'
        ))
        elements.append(title)
        elements.append(Spacer(1, 6 if full_width else 4))
        
        # Red header bar
        red_header_table = Table([["part of the owner"]], colWidths=[width_multiplier*inch])
        red_header_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, 0), red),
            ('TEXTCOLOR', (0, 0), (0, 0), white),
            ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (0, 0), 10 if full_width else 9),
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),
            ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (0, 0), 4 if full_width else 3),
            ('BOTTOMPADDING', (0, 0), (0, 0), 4 if full_width else 3),
        ]))
        elements.append(red_header_table)
        elements.append(Spacer(1, 8 if full_width else 6))
        
        # Receipt table
        if full_width:
            receipt_table = self.create_single_receipt_table(agreement_data, compact=True)
        else:
            receipt_table = self.create_compact_receipt_table(agreement_data)
        elements.append(receipt_table)
        elements.append(Spacer(1, 12 if full_width else 8))
        
        # Signature section
        signature_text = Paragraph('<para align="left" textColor="red" fontSize="9"><b>Signature of the tenant or person on his behalf</b></para>', 
                                 self.styles['Normal'])
        elements.append(signature_text)
        
        return elements
    
    def create_compact_receipt_table(self, agreement_data):
        """Create a more compact receipt table for side-by-side layout"""
        form_data = [
            # Row 1: No. and Date
            ["No.", "", "Date:", f"{agreement_data.agreement_date.strftime('%d/%m/%Y')}"],
            
            # Row 2: House/Shop/Flat and Holding No.
            ["House/Shop/Flat:", agreement_data.property_type, "Holding No.", agreement_data.holding_no],
            
            # Row 3: Owned by (spans across)
            ["Owned by:", agreement_data.owner_name[:20] + "..." if len(agreement_data.owner_name) > 20 else agreement_data.owner_name, "", ""],
            
            # Row 4: Village/Mahalla (spans across)
            ["Village/Mahalla:", agreement_data.village_area[:20] + "..." if len(agreement_data.village_area) > 20 else agreement_data.village_area, "", ""],
            
            # Row 5: Tenant Name (spans across)
            ["Tenant Name:", agreement_data.tenant_name[:20] + "..." if len(agreement_data.tenant_name) > 20 else agreement_data.tenant_name, "", ""],
            
            # Row 6: Father's Name (spans across)
            ["Father's Name:", agreement_data.father_name[:20] + "..." if len(agreement_data.father_name) > 20 else agreement_data.father_name, "", ""],
            
            # Row 7: Monthly Rent
            ["Monthly rent:", f"{agreement_data.monthly_rent:.2f}", "Money", ""],
            
            # Row 8: Bills
            ["Electricity:", f"{agreement_data.electricity_bill:.2f}", "Water:", f"{agreement_data.water_bill:.2f}"],
            ["Gas bill:", f"{agreement_data.gas_bill:.2f}", "", ""],
            
            # Row 9: Total
            ["Total bill:", f"{agreement_data.total_amount:.2f}", "received", ""],
        ]
        
        # Compact column widths
        col_widths = [0.8*inch, 0.9*inch, 0.8*inch, 0.9*inch]
        
        # Create table
        table = Table(form_data, colWidths=col_widths)
        
        # Table styling
        table.setStyle(TableStyle([
            # Basic table styling
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 7),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, black),
            ('LEFTPADDING', (0, 0), (-1, -1), 2),
            ('RIGHTPADDING', (0, 0), (-1, -1), 2),
            ('TOPPADDING', (0, 0), (-1, -1), 2),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 2),
            
            # Cell spanning for proper layout
            ('SPAN', (1, 2), (3, 2)),  # Owned by spans
            ('SPAN', (1, 3), (3, 3)),  # Village/Mahalla spans
            ('SPAN', (1, 4), (3, 4)),  # Tenant Name spans
            ('SPAN', (1, 5), (3, 5)),  # Father's Name spans
            ('SPAN', (2, 8), (3, 8)),  # Gas bill row
        ]))
        
        return table