from reportlab.lib.pagesizes import letter, A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from reportlab.lib.colors import black, red, blue
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from django.conf import settings
import os
from datetime import datetime

class HouseAgreementPDFGenerator:
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=black,
            fontName='Helvetica-Bold'
        )
        
        self.header_style = ParagraphStyle(
            'HeaderStyle',
            parent=self.styles['Normal'],
            fontSize=12,
            alignment=TA_CENTER,
            textColor=red,
            spaceAfter=20,
            fontName='Helvetica-Bold'
        )
        
        self.label_style = ParagraphStyle(
            'LabelStyle',
            parent=self.styles['Normal'],
            fontSize=11,
            alignment=TA_LEFT,
            spaceAfter=8
        )
        
        self.signature_style = ParagraphStyle(
            'SignatureStyle',
            parent=self.styles['Normal'],
            fontSize=11,
            alignment=TA_RIGHT,
            spaceAfter=8
        )
    
    def generate_pdf(self, agreement_data, filename=None):
        """
        Generate a house agreement PDF
        
        Args:
            agreement_data: HouseAgreement model instance
            filename: Optional custom filename
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"house_agreement_{agreement_data.tenant_name.replace(' ', '_')}_{timestamp}.pdf"
        
        # Ensure media directory exists
        media_dir = settings.MEDIA_ROOT
        os.makedirs(media_dir, exist_ok=True)
        
        filepath = os.path.join(media_dir, filename)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4, 
                              leftMargin=0.5*inch, rightMargin=0.5*inch,
                              topMargin=0.5*inch, bottomMargin=0.5*inch)
        story = []
        
        # Title
        title = Paragraph("HOUSE AGREEMENT", self.title_style)
        story.append(title)
        
        # Header text
        header = Paragraph("Agreement between Owner and Tenant", self.header_style)
        story.append(header)
        story.append(Spacer(1, 20))
        
        # Create form data table
        form_data = [
            ["Receipt No.", f"HA-{agreement_data.id:04d}", "Date:", agreement_data.agreement_date.strftime("%d/%m/%Y")],
            ["Property Type:", agreement_data.property_type, "Holding No.", agreement_data.holding_no],
            ["Owned by:", agreement_data.owner_name, "", ""],
            ["Village/Mahalla:", agreement_data.village_mahalla, "", ""],
            ["Tenant Name:", agreement_data.tenant_name, "", ""],
            ["Father's Name:", agreement_data.father_name, "", ""],
            ["Agreement Duration:", agreement_data.duration, "Monthly Rent:", f"₹{agreement_data.monthly_rent}"],
            ["Electricity Bill:", f"₹{agreement_data.electricity_bill}", "Water Bill:", f"₹{agreement_data.water_bill}"],
            ["Gas Bill:", f"₹{agreement_data.gas_bill}", "Total Amount:", f"₹{agreement_data.total_amount}"],
        ]
        
        # Create table
        table = Table(form_data, colWidths=[1.5*inch, 2.5*inch, 1.2*inch, 1.5*inch])
        
        # Table styling
        table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, black),
            ('BACKGROUND', (0, 0), (0, -1), '#f0f0f0'),  # Label column background
            ('BACKGROUND', (2, 0), (2, -1), '#f0f0f0'),  # Label column background
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),  # Bold labels
            ('FONTNAME', (2, 0), (2, -1), 'Helvetica-Bold'),  # Bold labels
        ]))
        
        story.append(table)
        story.append(Spacer(1, 40))
        
        # Terms and conditions
        terms = """
        <b>Terms and Conditions:</b><br/>
        1. The tenant agrees to pay the monthly rent on or before the 5th of each month.<br/>
        2. Utility bills (electricity, water, gas) are to be paid separately by the tenant.<br/>
        3. The tenant is responsible for maintaining the property in good condition.<br/>
        4. Any damages to the property will be deducted from the security deposit.<br/>
        5. Either party can terminate this agreement with 30 days written notice.<br/>
        6. This agreement is valid for the duration mentioned above.
        """
        
        terms_para = Paragraph(terms, self.label_style)
        story.append(terms_para)
        story.append(Spacer(1, 40))
        
        # Signature section
        signature_data = [
            ["", "", ""],
            ["_____________________", "", "_____________________"],
            ["Owner's Signature", "", "Tenant's Signature"],
            [agreement_data.owner_name, "", agreement_data.tenant_name],
            ["", "", ""],
            ["Date: _______________", "", "Date: _______________"],
        ]
        
        signature_table = Table(signature_data, colWidths=[2.5*inch, 1*inch, 2.5*inch])
        signature_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('FONTNAME', (0, 2), (-1, 2), 'Helvetica-Bold'),  # Bold signature labels
        ]))
        
        story.append(signature_table)
        
        # Generate PDF
        doc.build(story)
        
        return filename, filepath