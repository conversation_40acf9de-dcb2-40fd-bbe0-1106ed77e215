from reportlab.lib.pagesizes import letter, A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from reportlab.lib.colors import black, red, blue, white
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from django.conf import settings
import os
from datetime import datetime

class HouseAgreementPDFGenerator:
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=black,
            fontName='Helvetica-Bold'
        )
        
        self.header_style = ParagraphStyle(
            'HeaderStyle',
            parent=self.styles['Normal'],
            fontSize=12,
            alignment=TA_CENTER,
            textColor=red,
            spaceAfter=20,
            fontName='Helvetica-Bold'
        )
        
        self.label_style = ParagraphStyle(
            'LabelStyle',
            parent=self.styles['Normal'],
            fontSize=11,
            alignment=TA_LEFT,
            spaceAfter=8
        )
        
        self.signature_style = ParagraphStyle(
            'SignatureStyle',
            parent=self.styles['Normal'],
            fontSize=11,
            alignment=TA_RIGHT,
            spaceAfter=8
        )
    
    def generate_pdf(self, agreement_data, filename=None):
        """
        Generate a house agreement PDF
        
        Args:
            agreement_data: HouseAgreement model instance
            filename: Optional custom filename
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"house_agreement_{agreement_data.tenant_name.replace(' ', '_')}_{timestamp}.pdf"
        
        # Ensure media directory exists
        media_dir = settings.MEDIA_ROOT
        os.makedirs(media_dir, exist_ok=True)
        
        filepath = os.path.join(media_dir, filename)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4, 
                              leftMargin=0.5*inch, rightMargin=0.5*inch,
                              topMargin=0.5*inch, bottomMargin=0.5*inch)
        story = []
        
        # Title
        title = Paragraph("HOUSE AGREEMENT", self.title_style)
        story.append(title)
        
        # Header text
        header = Paragraph("Agreement between Owner and Tenant", self.header_style)
        story.append(header)
        story.append(Spacer(1, 20))
        
        # Create form data table
        form_data = [
            ["Receipt No.", f"HA-{agreement_data.id:04d}", "Date:", agreement_data.agreement_date.strftime("%d/%m/%Y")],
            ["Property Type:", agreement_data.property_type, "Holding No.", agreement_data.holding_no],
            ["Owned by:", agreement_data.owner_name, "", ""],
            ["Village/Mahalla:", agreement_data.village_mahalla, "", ""],
            ["Tenant Name:", agreement_data.tenant_name, "", ""],
            ["Father's Name:", agreement_data.father_name, "", ""],
            ["Agreement Duration:", agreement_data.duration, "Monthly Rent:", f"₹{agreement_data.monthly_rent}"],
            ["Electricity Bill:", f"₹{agreement_data.electricity_bill}", "Water Bill:", f"₹{agreement_data.water_bill}"],
            ["Gas Bill:", f"₹{agreement_data.gas_bill}", "Total Amount:", f"₹{agreement_data.total_amount}"],
        ]
        
        # Create table
        table = Table(form_data, colWidths=[1.5*inch, 2.5*inch, 1.2*inch, 1.5*inch])
        
        # Table styling
        table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, black),
            ('BACKGROUND', (0, 0), (0, -1), '#f0f0f0'),  # Label column background
            ('BACKGROUND', (2, 0), (2, -1), '#f0f0f0'),  # Label column background
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),  # Bold labels
            ('FONTNAME', (2, 0), (2, -1), 'Helvetica-Bold'),  # Bold labels
        ]))
        
        story.append(table)
        story.append(Spacer(1, 40))
        
        # Terms and conditions
        terms = """
        <b>Terms and Conditions:</b><br/>
        1. The tenant agrees to pay the monthly rent on or before the 5th of each month.<br/>
        2. Utility bills (electricity, water, gas) are to be paid separately by the tenant.<br/>
        3. The tenant is responsible for maintaining the property in good condition.<br/>
        4. Any damages to the property will be deducted from the security deposit.<br/>
        5. Either party can terminate this agreement with 30 days written notice.<br/>
        6. This agreement is valid for the duration mentioned above.
        """
        
        terms_para = Paragraph(terms, self.label_style)
        story.append(terms_para)
        story.append(Spacer(1, 40))
        
        # Signature section
        signature_data = [
            ["", "", ""],
            ["_____________________", "", "_____________________"],
            ["Owner's Signature", "", "Tenant's Signature"],
            [agreement_data.owner_name, "", agreement_data.tenant_name],
            ["", "", ""],
            ["Date: _______________", "", "Date: _______________"],
        ]
        
        signature_table = Table(signature_data, colWidths=[2.5*inch, 1*inch, 2.5*inch])
        signature_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('FONTNAME', (0, 2), (-1, 2), 'Helvetica-Bold'),  # Bold signature labels
        ]))
        
        story.append(signature_table)
        
        # Generate PDF
        doc.build(story)

        return filename, filepath

    def generate_multiple_receipts_pdf(self, receipt_data_list, filename=None):
        """Generate a PDF with multiple receipts - 2 per row, 4 per page maximum"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"multiple_receipts_{timestamp}.pdf"

        # Ensure media directory exists
        media_dir = settings.MEDIA_ROOT
        os.makedirs(media_dir, exist_ok=True)

        filepath = os.path.join(media_dir, filename)

        # Use tighter margins for multiple receipts
        doc = SimpleDocTemplate(filepath, pagesize=A4,
                              leftMargin=0.4*inch, rightMargin=0.4*inch,
                              topMargin=0.5*inch, bottomMargin=0.5*inch)
        story = []

        # Process receipts in groups of 4 (2 rows of 2 receipts each)
        for page_start in range(0, len(receipt_data_list), 4):
            page_receipts = receipt_data_list[page_start:page_start + 4]

            # Add page break if this is not the first page
            if page_start > 0:
                story.append(PageBreak())

            # Process receipts in pairs for each row
            for row_start in range(0, len(page_receipts), 2):
                row_receipts = page_receipts[row_start:row_start + 2]

                # Create horizontal layout for this row
                if len(row_receipts) == 2:
                    # Two receipts side by side
                    row_content = self._create_receipt_row(row_receipts[0], row_receipts[1])
                else:
                    # Single receipt (last one if odd number)
                    row_content = self._create_receipt_row(row_receipts[0], None)

                story.extend(row_content)

                # Add space between rows (but not after the last row on page)
                if row_start + 2 < len(page_receipts):
                    story.append(Spacer(1, 0.3*inch))

        # Generate PDF
        doc.build(story)
        return filename, filepath

    def _create_receipt_row(self, receipt1_data, receipt2_data=None):
        """Create a row with 1 or 2 receipts side by side"""
        row_content = []

        if receipt2_data is None:
            # Single receipt - use full width
            receipt_content = self._create_compact_receipt(receipt1_data, full_width=True)
            row_content.extend(receipt_content)
        else:
            # Two receipts side by side using a table layout
            left_receipt = self._create_compact_receipt_elements(receipt1_data)
            right_receipt = self._create_compact_receipt_elements(receipt2_data)

            # Create a table with two columns for side-by-side layout
            side_by_side_data = []
            max_elements = max(len(left_receipt), len(right_receipt))

            for i in range(max_elements):
                left_element = left_receipt[i] if i < len(left_receipt) else ""
                right_element = right_receipt[i] if i < len(right_receipt) else ""
                side_by_side_data.append([left_element, right_element])

            side_by_side_table = Table(side_by_side_data, colWidths=[3.6*inch, 3.6*inch])
            side_by_side_table.setStyle(TableStyle([
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 2),
                ('RIGHTPADDING', (0, 0), (-1, -1), 2),
                ('TOPPADDING', (0, 0), (-1, -1), 0),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
            ]))

            row_content.append(side_by_side_table)

        return row_content

    def _create_compact_receipt_elements(self, agreement_data):
        """Create individual receipt elements for side-by-side layout"""
        elements = []

        # Title
        title = Paragraph("Rent Receipt", ParagraphStyle(
            'CompactTitle',
            parent=self.styles['Heading1'],
            fontSize=12,
            spaceAfter=2,
            alignment=TA_CENTER,
            textColor=black,
            fontName='Helvetica-Bold'
        ))
        elements.append(title)
        elements.append(Spacer(1, 4))

        # Red header bar
        red_header_table = Table([["part of the owner"]], colWidths=[3.4*inch])
        red_header_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, 0), red),
            ('TEXTCOLOR', (0, 0), (0, 0), white),
            ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (0, 0), 9),
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),
            ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (0, 0), 3),
            ('BOTTOMPADDING', (0, 0), (0, 0), 3),
        ]))
        elements.append(red_header_table)
        elements.append(Spacer(1, 6))

        # Receipt table with compact styling
        receipt_table = self.create_compact_receipt_table(agreement_data)
        elements.append(receipt_table)
        elements.append(Spacer(1, 8))

        # Signature section
        signature_text = Paragraph('<para align="left" textColor="red" fontSize="8"><b>Signature of the tenant or person on his behalf</b></para>',
                                 self.styles['Normal'])
        elements.append(signature_text)

        return elements

    def create_compact_receipt_table(self, agreement_data):
        """Create a more compact receipt table for side-by-side layout"""
        form_data = [
            # Row 1: No. and Date
            ["No.", "", "Date:", f"{agreement_data.agreement_date.strftime('%d/%m/%Y')}"],

            # Row 2: House/Shop/Flat and Holding No.
            ["House/Shop/Flat:", agreement_data.property_type, "Holding No.", agreement_data.holding_no],

            # Row 3: Owned by (spans across)
            ["Owned by:", agreement_data.owner_name[:20] + "..." if len(agreement_data.owner_name) > 20 else agreement_data.owner_name, "", ""],

            # Row 4: Village/Mahalla (spans across)
            ["Village/Mahalla:", getattr(agreement_data, 'village_mahalla', 'Dangisara, Badalgachhi')[:20] + "..." if len(getattr(agreement_data, 'village_mahalla', 'Dangisara, Badalgachhi')) > 20 else getattr(agreement_data, 'village_mahalla', 'Dangisara, Badalgachhi'), "", ""],

            # Row 5: Tenant Name (spans across)
            ["Tenant Name:", agreement_data.tenant_name[:20] + "..." if len(agreement_data.tenant_name) > 20 else agreement_data.tenant_name, "", ""],

            # Row 6: Father's Name (spans across)
            ["Father's Name:", agreement_data.father_name[:20] + "..." if len(agreement_data.father_name) > 20 else agreement_data.father_name, "", ""],

            # Row 7: Monthly Rent and Electricity Bill
            ["Monthly Rent:", f"Tk. {agreement_data.monthly_rent}", "Electricity Bill:", f"Tk. {agreement_data.electricity_bill}"],

            # Row 8: Water Bill and Gas Bill
            ["Water Bill:", f"Tk. {agreement_data.water_bill}", "Gas Bill:", f"Tk. {agreement_data.gas_bill}"],

            # Row 9: Total Amount (spans across)
            ["Total Amount:", f"Tk. {agreement_data.total_amount}", "", ""],
        ]

        # Create table with compact column widths
        table = Table(form_data, colWidths=[0.8*inch, 1.2*inch, 0.8*inch, 1.2*inch])

        # Apply compact styling
        table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, black),
            ('BACKGROUND', (0, 0), (0, -1), '#f0f0f0'),  # Label column background
            ('BACKGROUND', (2, 0), (2, -1), '#f0f0f0'),  # Label column background
            ('LEFTPADDING', (0, 0), (-1, -1), 3),
            ('RIGHTPADDING', (0, 0), (-1, -1), 3),
            ('TOPPADDING', (0, 0), (-1, -1), 3),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),  # Bold labels
            ('FONTNAME', (2, 0), (2, -1), 'Helvetica-Bold'),  # Bold labels
        ]))

        return table

    def _create_compact_receipt(self, agreement_data, full_width=False):
        """Create a complete compact receipt"""
        elements = []
        width_multiplier = 7.2 if full_width else 3.4

        # Title
        title = Paragraph("Rent Receipt", ParagraphStyle(
            'CompactTitle',
            parent=self.styles['Heading1'],
            fontSize=14 if full_width else 12,
            spaceAfter=3,
            alignment=TA_CENTER,
            textColor=black,
            fontName='Helvetica-Bold'
        ))
        elements.append(title)
        elements.append(Spacer(1, 6 if full_width else 4))

        # Red header bar
        red_header_table = Table([["part of the owner"]], colWidths=[width_multiplier*inch])
        red_header_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, 0), red),
            ('TEXTCOLOR', (0, 0), (0, 0), white),
            ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (0, 0), 10 if full_width else 9),
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),
            ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (0, 0), 4 if full_width else 3),
            ('BOTTOMPADDING', (0, 0), (0, 0), 4 if full_width else 3),
        ]))

        elements.append(red_header_table)
        elements.append(Spacer(1, 8 if full_width else 6))

        # Receipt table
        if full_width:
            receipt_table = self.create_single_receipt_table(agreement_data)
        else:
            receipt_table = self.create_compact_receipt_table(agreement_data)
        elements.append(receipt_table)
        elements.append(Spacer(1, 12 if full_width else 8))

        # Signature section
        signature_text = Paragraph('<para align="left" textColor="red" fontSize="10"><b>Signature of the tenant or person on his behalf</b></para>' if full_width else '<para align="left" textColor="red" fontSize="8"><b>Signature of the tenant or person on his behalf</b></para>',
                                 self.styles['Normal'])
        elements.append(signature_text)

        return elements

    def create_single_receipt_table(self, agreement_data, compact=False):
        """Create a single receipt table structure"""
        form_data = [
            # Row 1: No. and Date
            ["No.", "", "", "Date:", f"{agreement_data.agreement_date.strftime('%d/%m/%Y')}", "", ""],

            # Row 2: House/Shop/Flat and Holding No.
            ["House/Shop/Flat:", agreement_data.property_type, "", "Holding No.", agreement_data.holding_no, "", ""],

            # Row 3: Owned by (spans across most columns)
            ["Owned by:", agreement_data.owner_name, "", "", "", "", ""],

            # Row 4: Village/Mahalla (spans across most columns)
            ["Village/Mahalla:", getattr(agreement_data, 'village_mahalla', 'Dangisara, Badalgachhi'), "", "", "", "", ""],

            # Row 5: Tenant Name (spans across most columns)
            ["Tenant Name:", agreement_data.tenant_name, "", "", "", "", ""],

            # Row 6: Father's Name (spans across most columns)
            ["Father's Name:", agreement_data.father_name, "", "", "", "", ""],

            # Row 7: Monthly Rent and Electricity Bill
            ["Monthly Rent:", f"Tk. {agreement_data.monthly_rent}", "", "Electricity Bill:", f"Tk. {agreement_data.electricity_bill}", "", ""],

            # Row 8: Water Bill and Gas Bill
            ["Water Bill:", f"Tk. {agreement_data.water_bill}", "", "Gas Bill:", f"Tk. {agreement_data.gas_bill}", "", ""],

            # Row 9: Total Amount (spans across most columns)
            ["Total Amount:", f"Tk. {agreement_data.total_amount}", "", "", "", "", ""],
        ]

        # Create table
        table = Table(form_data, colWidths=[0.8*inch, 1.5*inch, 0.3*inch, 0.8*inch, 1.5*inch, 0.3*inch, 0.8*inch])

        # Apply styling
        table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, black),
            ('BACKGROUND', (0, 0), (0, -1), '#f0f0f0'),  # Label column background
            ('BACKGROUND', (3, 0), (3, -1), '#f0f0f0'),  # Label column background
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),  # Bold labels
            ('FONTNAME', (3, 0), (3, -1), 'Helvetica-Bold'),  # Bold labels
        ]))

        return table