from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, FileResponse
from django.contrib import messages
from django.urls import reverse
from .models import HouseAgreement
from .forms import HouseAgreementForm, MultipleReceiptsForm
from .pdf_generator import HouseAgreementPDFGenerator
from .word_generator import WordReceiptGenerator
from datetime import datetime
import os

def create_agreement(request):
    """View to create a new house agreement"""
    if request.method == 'POST':
        form = HouseAgreementForm(request.POST)
        if form.is_valid():
            agreement = form.save()
            messages.success(request, 'House agreement created successfully!')
            return redirect('agreement_detail', pk=agreement.pk)
    else:
        form = HouseAgreementForm()
    
    return render(request, 'agreements/create_agreement.html', {'form': form})

def agreement_detail(request, pk):
    """View to display agreement details with PDF generation option"""
    agreement = get_object_or_404(HouseAgreement, pk=pk)
    return render(request, 'agreements/agreement_detail.html', {'agreement': agreement})

def generate_pdf(request, pk):
    """View to generate and download PDF for an agreement"""
    agreement = get_object_or_404(HouseAgreement, pk=pk)
    
    try:
        pdf_generator = HouseAgreementPDFGenerator()
        filename, filepath = pdf_generator.generate_pdf(agreement)
        
        # Return the PDF as a download
        response = FileResponse(
            open(filepath, 'rb'),
            as_attachment=True,
            filename=filename,
            content_type='application/pdf'
        )
        
        messages.success(request, f'PDF generated successfully: {filename}')
        return response
        
    except Exception as e:
        messages.error(request, f'Error generating PDF: {str(e)}')
        return redirect('agreement_detail', pk=pk)

def agreement_list(request):
    """View to list all agreements"""
    agreements = HouseAgreement.objects.all()
    return render(request, 'agreements/agreement_list.html', {'agreements': agreements})

def home(request):
    """Home page view"""
    recent_agreements = HouseAgreement.objects.all()[:5]
    return render(request, 'agreements/home.html', {'recent_agreements': recent_agreements})

def generate_word_receipt(request, pk):
    """View to generate and download Word receipt for an agreement"""
    agreement = get_object_or_404(HouseAgreement, pk=pk)
    
    try:
        word_generator = WordReceiptGenerator()
        filename, filepath = word_generator.generate_four_receipts_word(agreement)
        
        # Return the Word document as a download
        response = FileResponse(
            open(filepath, 'rb'),
            as_attachment=True,
            filename=filename,
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        
        messages.success(request, f'Word document with 4 receipts generated successfully: {filename}')
        return response
        
    except Exception as e:
        messages.error(request, f'Error generating Word document: {str(e)}')
        return redirect('agreement_detail', pk=pk)

def generate_multiple_word_receipts(request):
    """View to generate Word receipts for multiple tenants"""
    if request.method == 'POST':
        tenant_ids = request.POST.getlist('tenant_ids')
        months = request.POST.getlist('months')
        
        if not tenant_ids:
            messages.error(request, 'Please select at least one tenant.')
            return redirect('create_multiple_receipts')
        
        agreements = HouseAgreement.objects.filter(id__in=tenant_ids)
        
        try:
            word_generator = WordReceiptGenerator()
            filename, filepath = word_generator.generate_multiple_receipts_word(agreements, months)
            
            response = FileResponse(
                open(filepath, 'rb'),
                as_attachment=True,
                filename=filename,
                content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            )
            
            messages.success(request, f'Word document generated successfully: {filename}')
            return response
            
        except Exception as e:
            messages.error(request, f'Error generating Word document: {str(e)}')
            return redirect('create_multiple_receipts')
    
    return redirect('create_multiple_receipts')

def create_multiple_receipts(request):
    """View to create multiple different receipts for different tenants"""
    if request.method == 'POST':
        form = MultipleReceiptsForm(request.POST)
        if form.is_valid():
            file_format = form.cleaned_data['file_format']

            # Create list to store receipt data objects
            receipt_data_list = []

            # Check which receipts have data and process them
            for i in range(1, 5):  # Check all 4 possible receipts
                # Check if this receipt has required data
                tenant_name = form.cleaned_data.get(f'tenant_name_{i}')
                father_name = form.cleaned_data.get(f'father_name_{i}')
                monthly_rent = form.cleaned_data.get(f'monthly_rent_{i}')
                agreement_date = form.cleaned_data.get(f'agreement_date_{i}')
                property_type = form.cleaned_data.get(f'property_type_{i}')

                # If receipt has the minimum required data, include it
                if tenant_name and father_name and monthly_rent and agreement_date and property_type:
                    receipt_data = {
                        'property_type': property_type,
                        'tenant_name': tenant_name,
                        'father_name': father_name,
                        'monthly_rent': monthly_rent,
                        'electricity_bill': form.cleaned_data.get(f'electricity_bill_{i}') or 0,
                        'water_bill': form.cleaned_data.get(f'water_bill_{i}') or 0,
                        'gas_bill': form.cleaned_data.get(f'gas_bill_{i}') or 0,
                        'agreement_date': agreement_date,
                        'holding_no': '01663',
                        'owner_name': 'Md Abdul Mazed',
                        'village_ward': 'Dangisara, Badalgachhi'
                    }

                    # Create a temporary object-like structure for PDF generation
                    class TempReceiptData:
                        def __init__(self, data):
                            for key, value in data.items():
                                setattr(self, key, value)
                            self.id = i

                        @property
                        def total_amount(self):
                            return self.monthly_rent + self.electricity_bill + self.water_bill + self.gas_bill

                    receipt_obj = TempReceiptData(receipt_data)
                    receipt_data_list.append(receipt_obj)

            # Check if at least one receipt was created
            if not receipt_data_list:
                messages.error(request, 'Please fill out at least one complete receipt.')
                return render(request, 'agreements/create_multiple_receipts.html', {'form': form})

            # Generate the document with all receipts
            try:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                if file_format == 'docx':
                    word_generator = WordReceiptGenerator()
                    filename = f"multiple_receipts_{timestamp}.docx"
                    filename, filepath = word_generator.generate_multiple_receipts_word(receipt_data_list, filename)
                    content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                else:
                    pdf_generator = HouseAgreementPDFGenerator()
                    filename = f"multiple_receipts_{timestamp}.pdf"
                    filename, filepath = pdf_generator.generate_multiple_receipts_pdf(receipt_data_list, filename)
                    content_type = 'application/pdf'

                # Return the file as a download
                response = FileResponse(
                    open(filepath, 'rb'),
                    as_attachment=True,
                    filename=filename,
                    content_type=content_type
                )

                messages.success(request, f'Document generated successfully: {filename}')
                return response

            except Exception as e:
                messages.error(request, f'Error generating document: {str(e)}')
                return render(request, 'agreements/create_multiple_receipts.html', {'form': form})
    else:
        form = MultipleReceiptsForm()

    return render(request, 'agreements/create_multiple_receipts.html', {'form': form})
