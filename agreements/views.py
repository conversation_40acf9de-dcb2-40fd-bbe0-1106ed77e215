from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, FileResponse
from django.contrib import messages
from django.urls import reverse
from .models import HouseAgreement
from .forms import HouseAgreementForm
from .pdf_generator import HouseAgreementPDFGenerator
from .word_generator import WordReceiptGenerator
import os

def create_agreement(request):
    """View to create a new house agreement"""
    if request.method == 'POST':
        form = HouseAgreementForm(request.POST)
        if form.is_valid():
            agreement = form.save()
            messages.success(request, 'House agreement created successfully!')
            return redirect('agreement_detail', pk=agreement.pk)
    else:
        form = HouseAgreementForm()
    
    return render(request, 'agreements/create_agreement.html', {'form': form})

def agreement_detail(request, pk):
    """View to display agreement details with PDF generation option"""
    agreement = get_object_or_404(HouseAgreement, pk=pk)
    return render(request, 'agreements/agreement_detail.html', {'agreement': agreement})

def generate_pdf(request, pk):
    """View to generate and download PDF for an agreement"""
    agreement = get_object_or_404(HouseAgreement, pk=pk)
    
    try:
        pdf_generator = HouseAgreementPDFGenerator()
        filename, filepath = pdf_generator.generate_pdf(agreement)
        
        # Return the PDF as a download
        response = FileResponse(
            open(filepath, 'rb'),
            as_attachment=True,
            filename=filename,
            content_type='application/pdf'
        )
        
        messages.success(request, f'PDF generated successfully: {filename}')
        return response
        
    except Exception as e:
        messages.error(request, f'Error generating PDF: {str(e)}')
        return redirect('agreement_detail', pk=pk)

def agreement_list(request):
    """View to list all agreements"""
    agreements = HouseAgreement.objects.all()
    return render(request, 'agreements/agreement_list.html', {'agreements': agreements})

def home(request):
    """Home page view"""
    recent_agreements = HouseAgreement.objects.all()[:5]
    return render(request, 'agreements/home.html', {'recent_agreements': recent_agreements})

def generate_word_receipt(request, pk):
    """View to generate and download Word receipt for an agreement"""
    agreement = get_object_or_404(HouseAgreement, pk=pk)
    
    try:
        word_generator = WordReceiptGenerator()
        filename, filepath = word_generator.generate_four_receipts_word(agreement)
        
        # Return the Word document as a download
        response = FileResponse(
            open(filepath, 'rb'),
            as_attachment=True,
            filename=filename,
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        
        messages.success(request, f'Word document with 4 receipts generated successfully: {filename}')
        return response
        
    except Exception as e:
        messages.error(request, f'Error generating Word document: {str(e)}')
        return redirect('agreement_detail', pk=pk)

def generate_multiple_word_receipts(request):
    """View to generate Word receipts for multiple tenants"""
    if request.method == 'POST':
        tenant_ids = request.POST.getlist('tenant_ids')
        months = request.POST.getlist('months')
        
        if not tenant_ids:
            messages.error(request, 'Please select at least one tenant.')
            return redirect('create_multiple_receipts')
        
        agreements = HouseAgreement.objects.filter(id__in=tenant_ids)
        
        try:
            word_generator = WordReceiptGenerator()
            filename, filepath = word_generator.generate_multiple_receipts_word(agreements, months)
            
            response = FileResponse(
                open(filepath, 'rb'),
                as_attachment=True,
                filename=filename,
                content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            )
            
            messages.success(request, f'Word document generated successfully: {filename}')
            return response
            
        except Exception as e:
            messages.error(request, f'Error generating Word document: {str(e)}')
            return redirect('create_multiple_receipts')
    
    return redirect('create_multiple_receipts')

def create_multiple_receipts(request):
    """View to create multiple receipts page"""
    agreements = HouseAgreement.objects.all()
    return render(request, 'agreements/create_multiple_receipts.html', {'agreements': agreements})
