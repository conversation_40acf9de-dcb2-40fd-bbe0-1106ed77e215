from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, FileResponse
from django.contrib import messages
from django.urls import reverse
from .models import HouseAgreement
from .forms import HouseAgreementForm, MultipleReceiptsForm
from .pdf_generator import HouseAgreementPDFGenerator
from .word_generator import WordDocumentGenerator
import os
from datetime import datetime

def create_agreement(request):
    """View to create a new house agreement"""
    if request.method == 'POST':
        form = HouseAgreementForm(request.POST)
        if form.is_valid():
            agreement = form.save()
            
            # Get form data for receipts per page and file format
            receipts_per_page = form.cleaned_data.get('receipts_per_page', 1)
            file_format = form.cleaned_data.get('file_format', 'pdf')
            
            # Generate the document based on selected format
            try:
                if file_format == 'docx':
                    word_generator = WordDocumentGenerator()
                    filename, filepath = word_generator.generate_word_document(
                        agreement, receipts_per_page
                    )
                    content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                else:
                    pdf_generator = HouseAgreementPDFGenerator()
                    filename, filepath = pdf_generator.generate_pdf(
                        agreement, receipts_per_page
                    )
                    content_type = 'application/pdf'
                
                # Return the file as a download
                response = FileResponse(
                    open(filepath, 'rb'),
                    as_attachment=True,
                    filename=filename,
                    content_type=content_type
                )
                
                messages.success(request, f'Document generated successfully: {filename}')
                return response
                
            except Exception as e:
                messages.error(request, f'Error generating document: {str(e)}')
                return redirect('agreement_detail', pk=agreement.pk)
    else:
        form = HouseAgreementForm()
    
    return render(request, 'agreements/create_agreement.html', {'form': form})

def agreement_detail(request, pk):
    """View to display agreement details with PDF generation option"""
    agreement = get_object_or_404(HouseAgreement, pk=pk)
    return render(request, 'agreements/agreement_detail.html', {'agreement': agreement})

def generate_pdf(request, pk):
    """View to generate and download PDF for an agreement"""
    agreement = get_object_or_404(HouseAgreement, pk=pk)
    receipts_per_page = request.GET.get('receipts', 1)
    
    try:
        pdf_generator = HouseAgreementPDFGenerator()
        filename, filepath = pdf_generator.generate_pdf(agreement, receipts_per_page)
        
        # Return the PDF as a download
        response = FileResponse(
            open(filepath, 'rb'),
            as_attachment=True,
            filename=filename,
            content_type='application/pdf'
        )
        
        messages.success(request, f'PDF generated successfully: {filename}')
        return response
        
    except Exception as e:
        messages.error(request, f'Error generating PDF: {str(e)}')
        return redirect('agreement_detail', pk=pk)

def generate_word(request, pk):
    """View to generate and download Word document for an agreement"""
    agreement = get_object_or_404(HouseAgreement, pk=pk)
    receipts_per_page = request.GET.get('receipts', 1)
    
    try:
        word_generator = WordDocumentGenerator()
        filename, filepath = word_generator.generate_word_document(agreement, receipts_per_page)
        
        # Return the Word document as a download
        response = FileResponse(
            open(filepath, 'rb'),
            as_attachment=True,
            filename=filename,
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        
        messages.success(request, f'Word document generated successfully: {filename}')
        return response
        
    except Exception as e:
        messages.error(request, f'Error generating Word document: {str(e)}')
        return redirect('agreement_detail', pk=pk)

def agreement_list(request):
    """View to list all agreements"""
    agreements = HouseAgreement.objects.all()
    return render(request, 'agreements/agreement_list.html', {'agreements': agreements})

def home(request):
    """Home page view"""
    recent_agreements = HouseAgreement.objects.all()[:5]
    return render(request, 'agreements/home.html', {'recent_agreements': recent_agreements})

def create_multiple_receipts(request):
    """View to create multiple different receipts for different tenants"""
    if request.method == 'POST':
        form = MultipleReceiptsForm(request.POST)
        if form.is_valid():
            file_format = form.cleaned_data['file_format']
            
            # Create list to store receipt data objects
            receipt_data_list = []
            
            # Check which receipts have data and process them
            for i in range(1, 5):  # Check all 4 possible receipts
                # Check if this receipt has required data
                tenant_name = form.cleaned_data.get(f'tenant_name_{i}')
                father_name = form.cleaned_data.get(f'father_name_{i}')
                monthly_rent = form.cleaned_data.get(f'monthly_rent_{i}')
                agreement_date = form.cleaned_data.get(f'agreement_date_{i}')
                property_type = form.cleaned_data.get(f'property_type_{i}')
                
                # If receipt has the minimum required data, include it
                if tenant_name and father_name and monthly_rent and agreement_date and property_type:
                    receipt_data = {
                        'property_type': property_type,
                        'tenant_name': tenant_name,
                        'father_name': father_name,
                        'monthly_rent': monthly_rent,
                        'electricity_bill': form.cleaned_data.get(f'electricity_bill_{i}') or 0,
                        'water_bill': form.cleaned_data.get(f'water_bill_{i}') or 0,
                        'gas_bill': form.cleaned_data.get(f'gas_bill_{i}') or 0,
                        'agreement_date': agreement_date,
                        'holding_no': '01663',
                        'owner_name': 'Md Abdul Mazed',
                        'village_area': 'Dangisara, Badalgachhi'
                    }
                    
                    # Create a temporary object-like structure for PDF generation
                    class TempReceiptData:
                        def __init__(self, data):
                            for key, value in data.items():
                                setattr(self, key, value)
                            self.id = i
                        
                        @property
                        def total_amount(self):
                            return self.monthly_rent + self.electricity_bill + self.water_bill + self.gas_bill
                    
                    receipt_obj = TempReceiptData(receipt_data)
                    receipt_data_list.append(receipt_obj)
            
            # Check if at least one receipt was created
            if not receipt_data_list:
                messages.error(request, 'Please fill out at least one complete receipt.')
                return render(request, 'agreements/create_multiple_receipts.html', {'form': form})
            
            # Generate the document with all receipts
            try:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                
                if file_format == 'docx':
                    word_generator = WordDocumentGenerator()
                    filename = f"multiple_receipts_{timestamp}.docx"
                    filepath = word_generator.generate_multiple_receipts_document(receipt_data_list, filename)
                    content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                else:
                    pdf_generator = HouseAgreementPDFGenerator()
                    filename = f"multiple_receipts_{timestamp}.pdf"
                    filepath = pdf_generator.generate_multiple_receipts_pdf(receipt_data_list, filename)
                    content_type = 'application/pdf'
                
                # Return the file as a download
                response = FileResponse(
                    open(filepath, 'rb'),
                    as_attachment=True,
                    filename=filename,
                    content_type=content_type
                )
                
                messages.success(request, f'{len(receipt_data_list)} receipts generated successfully: {filename}')
                return response
                
            except Exception as e:
                messages.error(request, f'Error generating document: {str(e)}')
                return redirect('create_multiple_receipts')
    else:
        form = MultipleReceiptsForm()
    
    return render(request, 'agreements/create_multiple_receipts.html', {'form': form})
