from django import forms
from .models import HouseAgreement

class HouseAgreementForm(forms.ModelForm):
    class Meta:
        model = HouseAgreement
        fields = [
            'property_type', 'holding_no', 'owner_name', 'village_mahalla',
            'tenant_name', 'father_name', 'monthly_rent', 'electricity_bill',
            'water_bill', 'gas_bill', 'duration', 'agreement_date'
        ]
        
        widgets = {
            'property_type': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., House/Shop/Flat'
            }),
            'holding_no': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Holding Number'
            }),
            'owner_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Owner Full Name'
            }),
            'village_mahalla': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Village/Mahalla Name'
            }),
            'tenant_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Tenant Full Name'
            }),
            'father_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': "Father's Full Name"
            }),
            'monthly_rent': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Monthly Rent Amount',
                'step': '0.01'
            }),
            'electricity_bill': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Electricity Bill Amount',
                'step': '0.01'
            }),
            'water_bill': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Water Bill Amount',
                'step': '0.01'
            }),
            'gas_bill': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Gas Bill Amount',
                'step': '0.01'
            }),
            'duration': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., 1 year, 6 months'
            }),
            'agreement_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }
        
        labels = {
            'property_type': 'Property Type',
            'holding_no': 'Holding Number',
            'owner_name': 'Owner Name',
            'village_mahalla': 'Village/Mahalla',
            'tenant_name': 'Tenant Name',
            'father_name': "Father's Name",
            'monthly_rent': 'Monthly Rent (₹)',
            'electricity_bill': 'Electricity Bill (₹)',
            'water_bill': 'Water Bill (₹)',
            'gas_bill': 'Gas Bill (₹)',
            'duration': 'Agreement Duration',
            'agreement_date': 'Agreement Date',
        }