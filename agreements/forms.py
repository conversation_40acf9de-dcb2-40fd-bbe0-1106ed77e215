from django import forms
from .models import HouseAgreement

class MultipleReceiptsForm(forms.Form):
    # Add field for file format only
    file_format = forms.ChoiceField(
        choices=[('pdf', 'PDF Format'), ('docx', 'Word Document (DOCX)')],
        initial='pdf',
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='Download Format'
    )
    
    # Receipt 1 fields (always required)
    property_type_1 = forms.ChoiceField(
        choices=[('House', 'House'), ('Shop', 'Shop'), ('Flat', 'Flat')],
        widget=forms.Select(attrs={'class': 'form-select property-type', 'data-receipt': '1'}),
        label='Property Type'
    )
    tenant_name_1 = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={'class': 'form-control tenant-name', 'data-receipt': '1'}),
        label='Tenant Name'
    )
    father_name_1 = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={'class': 'form-control father-name', 'data-receipt': '1'}),
        label="Father's Name"
    )
    monthly_rent_1 = forms.DecimalField(
        max_digits=10, decimal_places=2,
        widget=forms.NumberInput(attrs={'class': 'form-control monthly-rent', 'data-receipt': '1', 'step': '0.01'}),
        label='Monthly Rent (Taka)'
    )
    electricity_bill_1 = forms.DecimalField(
        max_digits=10, decimal_places=2, initial=0,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        label='Electricity Bill (Taka)'
    )
    water_bill_1 = forms.DecimalField(
        max_digits=10, decimal_places=2, initial=0,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        label='Water Bill (Taka)'
    )
    gas_bill_1 = forms.DecimalField(
        max_digits=10, decimal_places=2, initial=0,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        label='Gas Bill (Taka)'
    )
    agreement_date_1 = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label='Date'
    )
    
    # Receipt 2 fields (optional)
    property_type_2 = forms.ChoiceField(
        choices=[('House', 'House'), ('Shop', 'Shop'), ('Flat', 'Flat')],
        widget=forms.Select(attrs={'class': 'form-select property-type', 'data-receipt': '2'}),
        label='Property Type',
        required=False
    )
    tenant_name_2 = forms.CharField(
        max_length=200, required=False,
        widget=forms.TextInput(attrs={'class': 'form-control tenant-name', 'data-receipt': '2'}),
        label='Tenant Name'
    )
    father_name_2 = forms.CharField(
        max_length=200, required=False,
        widget=forms.TextInput(attrs={'class': 'form-control father-name', 'data-receipt': '2'}),
        label="Father's Name"
    )
    monthly_rent_2 = forms.DecimalField(
        max_digits=10, decimal_places=2, required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control monthly-rent', 'data-receipt': '2', 'step': '0.01'}),
        label='Monthly Rent (Taka)'
    )
    electricity_bill_2 = forms.DecimalField(
        max_digits=10, decimal_places=2, initial=0, required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        label='Electricity Bill (Taka)'
    )
    water_bill_2 = forms.DecimalField(
        max_digits=10, decimal_places=2, initial=0, required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        label='Water Bill (Taka)'
    )
    gas_bill_2 = forms.DecimalField(
        max_digits=10, decimal_places=2, initial=0, required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        label='Gas Bill (Taka)'
    )
    agreement_date_2 = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label='Date'
    )
    
    # Receipt 3 fields (optional)
    property_type_3 = forms.ChoiceField(
        choices=[('House', 'House'), ('Shop', 'Shop'), ('Flat', 'Flat')],
        widget=forms.Select(attrs={'class': 'form-select property-type', 'data-receipt': '3'}),
        label='Property Type',
        required=False
    )
    tenant_name_3 = forms.CharField(
        max_length=200, required=False,
        widget=forms.TextInput(attrs={'class': 'form-control tenant-name', 'data-receipt': '3'}),
        label='Tenant Name'
    )
    father_name_3 = forms.CharField(
        max_length=200, required=False,
        widget=forms.TextInput(attrs={'class': 'form-control father-name', 'data-receipt': '3'}),
        label="Father's Name"
    )
    monthly_rent_3 = forms.DecimalField(
        max_digits=10, decimal_places=2, required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control monthly-rent', 'data-receipt': '3', 'step': '0.01'}),
        label='Monthly Rent (Taka)'
    )
    electricity_bill_3 = forms.DecimalField(
        max_digits=10, decimal_places=2, initial=0, required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        label='Electricity Bill (Taka)'
    )
    water_bill_3 = forms.DecimalField(
        max_digits=10, decimal_places=2, initial=0, required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        label='Water Bill (Taka)'
    )
    gas_bill_3 = forms.DecimalField(
        max_digits=10, decimal_places=2, initial=0, required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        label='Gas Bill (Taka)'
    )
    agreement_date_3 = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label='Date'
    )
    
    # Receipt 4 fields (optional)
    property_type_4 = forms.ChoiceField(
        choices=[('House', 'House'), ('Shop', 'Shop'), ('Flat', 'Flat')],
        widget=forms.Select(attrs={'class': 'form-select property-type', 'data-receipt': '4'}),
        label='Property Type',
        required=False
    )
    tenant_name_4 = forms.CharField(
        max_length=200, required=False,
        widget=forms.TextInput(attrs={'class': 'form-control tenant-name', 'data-receipt': '4'}),
        label='Tenant Name'
    )
    father_name_4 = forms.CharField(
        max_length=200, required=False,
        widget=forms.TextInput(attrs={'class': 'form-control father-name', 'data-receipt': '4'}),
        label="Father's Name"
    )
    monthly_rent_4 = forms.DecimalField(
        max_digits=10, decimal_places=2, required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control monthly-rent', 'data-receipt': '4', 'step': '0.01'}),
        label='Monthly Rent (Taka)'
    )
    electricity_bill_4 = forms.DecimalField(
        max_digits=10, decimal_places=2, initial=0, required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        label='Electricity Bill (Taka)'
    )
    water_bill_4 = forms.DecimalField(
        max_digits=10, decimal_places=2, initial=0, required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        label='Water Bill (Taka)'
    )
    gas_bill_4 = forms.DecimalField(
        max_digits=10, decimal_places=2, initial=0, required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        label='Gas Bill (Taka)'
    )
    agreement_date_4 = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label='Date'
    )

# Keep the original single agreement form as well
class HouseAgreementForm(forms.ModelForm):
    # Add field for number of receipts per page
    receipts_per_page = forms.ChoiceField(
        choices=[(1, '1 Receipt'), (2, '2 Receipts'), (3, '3 Receipts'), (4, '4 Receipts')],
        initial=1,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='Number of Receipts per Page'
    )
    
    # Add field for file format
    file_format = forms.ChoiceField(
        choices=[('pdf', 'PDF Format'), ('docx', 'Word Document (DOCX)')],
        initial='pdf',
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='Download Format'
    )
    
    class Meta:
        model = HouseAgreement
        fields = [
            'property_type', 'tenant_name', 'father_name',
            'monthly_rent', 'electricity_bill', 'water_bill', 'gas_bill', 'agreement_date'
        ]
        
        widgets = {
            'property_type': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
            'tenant_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter tenant full name',
                'required': True
            }),
            'father_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': "Enter father's full name",
                'required': True
            }),
            'monthly_rent': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter monthly rent amount',
                'step': '0.01',
                'min': '0'
            }),
            'electricity_bill': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter electricity bill amount',
                'step': '0.01',
                'min': '0'
            }),
            'water_bill': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter water bill amount',
                'step': '0.01',
                'min': '0'
            }),
            'gas_bill': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter gas bill amount',
                'step': '0.01',
                'min': '0'
            }),
            'agreement_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }
        
        labels = {
            'property_type': 'House/Shop/Flat',
            'tenant_name': 'Tenant Name',
            'father_name': "Father's Name",
            'monthly_rent': 'Month Rent Monthly (Taka)',
            'electricity_bill': 'Electricity Bill (Taka)',
            'water_bill': 'Water Bill (Taka)',
            'gas_bill': 'Gas Bill (Taka)',
            'agreement_date': 'Date',
        }