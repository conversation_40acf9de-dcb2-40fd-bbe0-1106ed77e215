from django.contrib import admin
from .models import HouseAgreement

@admin.register(HouseAgreement)
class HouseAgreementAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'tenant_name', 'owner_name', 'property_type', 
        'monthly_rent', 'total_amount', 'agreement_date', 'created_at'
    ]
    list_filter = ['property_type', 'agreement_date', 'created_at']
    search_fields = ['tenant_name', 'owner_name', 'property_type', 'village_mahalla']
    readonly_fields = ['created_at', 'updated_at', 'total_amount']
    
    fieldsets = (
        ('Property Information', {
            'fields': ('property_type', 'holding_no', 'owner_name', 'village_mahalla')
        }),
        ('Tenant Information', {
            'fields': ('tenant_name', 'father_name')
        }),
        ('Financial Details', {
            'fields': ('monthly_rent', 'electricity_bill', 'water_bill', 'gas_bill')
        }),
        ('Agreement Details', {
            'fields': ('duration', 'agreement_date')
        }),
        ('System Information', {
            'fields': ('total_amount', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def total_amount(self, obj):
        return f"₹{obj.total_amount}"
    total_amount.short_description = "Total Amount"
