{% extends 'agreements/base.html' %}
{% load static %}

{% block title %}Create Rent Receipt - House Agreement Generator{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-file-earmark-plus"></i> Create New Rent Receipt
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Display Fixed Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-success border-bottom pb-2 mb-3">
                                <i class="bi bi-info-circle"></i> Fixed Property Information
                            </h5>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <strong>Holding No:</strong> 01663
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <strong>Owner Name:</strong> Md Abdul Mazed
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 mb-3">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <strong>Village/Area:</strong> Dangisara, Badalgachhi
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="post" id="agreementForm">
                        {% csrf_token %}
                        
                        <!-- Property Type Selection -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-house"></i> Property Type & Unit Selection
                                </h5>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.property_type.id_for_label }}" class="form-label">{{ form.property_type.label }} *</label>
                                {{ form.property_type }}
                                {% if form.property_type.errors %}
                                    <div class="text-danger">{{ form.property_type.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3" id="unit_container" style="display: none;">
                                <label for="unit_select" class="form-label">Select Unit *</label>
                                <select id="unit_select" class="form-select">
                                    <option value="">Select Unit</option>
                                </select>
                                <small class="text-muted">Choose a unit to auto-fill tenant information</small>
                            </div>
                        </div>

                        <!-- Tenant Information Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-person"></i> Tenant Information
                                </h5>
                                <div class="alert alert-info">
                                    <i class="bi bi-lightbulb"></i> 
                                    <strong>Tip:</strong> Select a property type and unit above to automatically fill tenant information, or enter manually.
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.tenant_name.id_for_label }}" class="form-label">{{ form.tenant_name.label }} *</label>
                                {{ form.tenant_name }}
                                {% if form.tenant_name.errors %}
                                    <div class="text-danger">{{ form.tenant_name.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.father_name.id_for_label }}" class="form-label">{{ form.father_name.label }} *</label>
                                {{ form.father_name }}
                                {% if form.father_name.errors %}
                                    <div class="text-danger">{{ form.father_name.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Financial Details Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-currency-exchange"></i> Financial Details (in Taka)
                                </h5>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.monthly_rent.id_for_label }}" class="form-label">{{ form.monthly_rent.label }} *</label>
                                <div class="input-group">
                                    <span class="input-group-text">৳</span>
                                    {{ form.monthly_rent }}
                                </div>
                                {% if form.monthly_rent.errors %}
                                    <div class="text-danger">{{ form.monthly_rent.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.electricity_bill.id_for_label }}" class="form-label">{{ form.electricity_bill.label }}</label>
                                <div class="input-group">
                                    <span class="input-group-text">৳</span>
                                    {{ form.electricity_bill }}
                                </div>
                                {% if form.electricity_bill.errors %}
                                    <div class="text-danger">{{ form.electricity_bill.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.water_bill.id_for_label }}" class="form-label">{{ form.water_bill.label }}</label>
                                <div class="input-group">
                                    <span class="input-group-text">৳</span>
                                    {{ form.water_bill }}
                                </div>
                                {% if form.water_bill.errors %}
                                    <div class="text-danger">{{ form.water_bill.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.gas_bill.id_for_label }}" class="form-label">{{ form.gas_bill.label }}</label>
                                <div class="input-group">
                                    <span class="input-group-text">৳</span>
                                    {{ form.gas_bill }}
                                </div>
                                {% if form.gas_bill.errors %}
                                    <div class="text-danger">{{ form.gas_bill.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Generation Options Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-gear"></i> Generation Options
                                </h5>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.receipts_per_page.id_for_label }}" class="form-label">{{ form.receipts_per_page.label }} *</label>
                                {{ form.receipts_per_page }}
                                {% if form.receipts_per_page.errors %}
                                    <div class="text-danger">{{ form.receipts_per_page.errors }}</div>
                                {% endif %}
                                <small class="text-muted">Select how many receipts to include on each page</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.file_format.id_for_label }}" class="form-label">{{ form.file_format.label }} *</label>
                                {{ form.file_format }}
                                {% if form.file_format.errors %}
                                    <div class="text-danger">{{ form.file_format.errors }}</div>
                                {% endif %}
                                <small class="text-muted">Choose between PDF or Word document format</small>
                            </div>
                        </div>

                        <!-- Date Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-calendar"></i> Receipt Date
                                </h5>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.agreement_date.id_for_label }}" class="form-label">{{ form.agreement_date.label }} *</label>
                                {{ form.agreement_date }}
                                {% if form.agreement_date.errors %}
                                    <div class="text-danger">{{ form.agreement_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Total Amount Display -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-success">
                                    <h6 class="mb-2">
                                        <i class="bi bi-calculator"></i> Total Monthly Amount
                                    </h6>
                                    <p class="mb-0 fs-5"><strong>Total: ৳<span id="totalAmount">0.00</span> Taka</strong></p>
                                    <small class="text-muted">This includes rent + electricity + water + gas bills</small>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'home' %}" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-left"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="bi bi-check-circle"></i> Create Receipt & Generate PDF
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include the tenant selector JavaScript -->
<script src="{% static 'js/tenant_selector.js' %}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate total amount
    function calculateTotal() {
        const monthlyRent = parseFloat(document.getElementById('id_monthly_rent').value) || 0;
        const electricityBill = parseFloat(document.getElementById('id_electricity_bill').value) || 0;
        const waterBill = parseFloat(document.getElementById('id_water_bill').value) || 0;
        const gasBill = parseFloat(document.getElementById('id_gas_bill').value) || 0;
        
        const total = monthlyRent + electricityBill + waterBill + gasBill;
        document.getElementById('totalAmount').textContent = total.toFixed(2);
    }

    // Add event listeners to financial input fields
    const financialFields = ['id_monthly_rent', 'id_electricity_bill', 'id_water_bill', 'id_gas_bill'];
    financialFields.forEach(function(fieldId) {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', calculateTotal);
            field.addEventListener('change', calculateTotal);
        }
    });

    // Initial calculation
    calculateTotal();

    // Form validation
    document.getElementById('agreementForm').addEventListener('submit', function(e) {
        let hasErrors = false;
        const requiredFields = ['id_property_type', 'id_tenant_name', 'id_father_name', 'id_monthly_rent', 'id_agreement_date'];
        
        requiredFields.forEach(function(fieldId) {
            const field = document.getElementById(fieldId);
            if (field && !field.value.trim()) {
                hasErrors = true;
                field.classList.add('is-invalid');
            } else if (field) {
                field.classList.remove('is-invalid');
            }
        });

        if (hasErrors) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });

    // Set today's date as default
    const dateField = document.getElementById('id_agreement_date');
    if (dateField && !dateField.value) {
        const today = new Date().toISOString().split('T')[0];
        dateField.value = today;
    }
});
</script>
{% endblock %}