{% extends 'agreements/base.html' %}

{% block title %}Create Agreement - House Agreement Generator{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-file-earmark-plus"></i> Create New House Agreement
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post" id="agreementForm">
                        {% csrf_token %}
                        
                        <!-- Property Information Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-house"></i> Property Information
                                </h5>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.property_type.id_for_label }}" class="form-label">{{ form.property_type.label }}</label>
                                {{ form.property_type }}
                                {% if form.property_type.errors %}
                                    <div class="text-danger">{{ form.property_type.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.holding_no.id_for_label }}" class="form-label">{{ form.holding_no.label }}</label>
                                {{ form.holding_no }}
                                {% if form.holding_no.errors %}
                                    <div class="text-danger">{{ form.holding_no.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.owner_name.id_for_label }}" class="form-label">{{ form.owner_name.label }}</label>
                                {{ form.owner_name }}
                                {% if form.owner_name.errors %}
                                    <div class="text-danger">{{ form.owner_name.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.village_mahalla.id_for_label }}" class="form-label">{{ form.village_mahalla.label }}</label>
                                {{ form.village_mahalla }}
                                {% if form.village_mahalla.errors %}
                                    <div class="text-danger">{{ form.village_mahalla.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Tenant Information Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-person"></i> Tenant Information
                                </h5>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.tenant_name.id_for_label }}" class="form-label">{{ form.tenant_name.label }}</label>
                                {{ form.tenant_name }}
                                {% if form.tenant_name.errors %}
                                    <div class="text-danger">{{ form.tenant_name.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.father_name.id_for_label }}" class="form-label">{{ form.father_name.label }}</label>
                                {{ form.father_name }}
                                {% if form.father_name.errors %}
                                    <div class="text-danger">{{ form.father_name.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Financial Details Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-currency-rupee"></i> Financial Details
                                </h5>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.monthly_rent.id_for_label }}" class="form-label">{{ form.monthly_rent.label }}</label>
                                {{ form.monthly_rent }}
                                {% if form.monthly_rent.errors %}
                                    <div class="text-danger">{{ form.monthly_rent.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.electricity_bill.id_for_label }}" class="form-label">{{ form.electricity_bill.label }}</label>
                                {{ form.electricity_bill }}
                                {% if form.electricity_bill.errors %}
                                    <div class="text-danger">{{ form.electricity_bill.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.water_bill.id_for_label }}" class="form-label">{{ form.water_bill.label }}</label>
                                {{ form.water_bill }}
                                {% if form.water_bill.errors %}
                                    <div class="text-danger">{{ form.water_bill.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.gas_bill.id_for_label }}" class="form-label">{{ form.gas_bill.label }}</label>
                                {{ form.gas_bill }}
                                {% if form.gas_bill.errors %}
                                    <div class="text-danger">{{ form.gas_bill.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Agreement Details Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-calendar"></i> Agreement Details
                                </h5>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.duration.id_for_label }}" class="form-label">{{ form.duration.label }}</label>
                                {{ form.duration }}
                                {% if form.duration.errors %}
                                    <div class="text-danger">{{ form.duration.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.agreement_date.id_for_label }}" class="form-label">{{ form.agreement_date.label }}</label>
                                {{ form.agreement_date }}
                                {% if form.agreement_date.errors %}
                                    <div class="text-danger">{{ form.agreement_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Total Amount Display -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6 class="mb-2">
                                        <i class="bi bi-calculator"></i> Total Monthly Amount
                                    </h6>
                                    <p class="mb-0">Total: ₹<span id="totalAmount">0.00</span></p>
                                    <small class="text-muted">This includes rent + electricity + water + gas bills</small>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'home' %}" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-left"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="bi bi-check-circle"></i> Create Agreement
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate total amount
    function calculateTotal() {
        const monthlyRent = parseFloat(document.getElementById('id_monthly_rent').value) || 0;
        const electricityBill = parseFloat(document.getElementById('id_electricity_bill').value) || 0;
        const waterBill = parseFloat(document.getElementById('id_water_bill').value) || 0;
        const gasBill = parseFloat(document.getElementById('id_gas_bill').value) || 0;
        
        const total = monthlyRent + electricityBill + waterBill + gasBill;
        document.getElementById('totalAmount').textContent = total.toFixed(2);
    }

    // Add event listeners to financial input fields
    const financialFields = ['id_monthly_rent', 'id_electricity_bill', 'id_water_bill', 'id_gas_bill'];
    financialFields.forEach(function(fieldId) {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', calculateTotal);
            field.addEventListener('change', calculateTotal);
        }
    });

    // Initial calculation
    calculateTotal();

    // Form validation
    document.getElementById('agreementForm').addEventListener('submit', function(e) {
        let hasErrors = false;
        const requiredFields = ['id_property_type', 'id_owner_name', 'id_village_mahalla', 'id_tenant_name', 'id_father_name', 'id_monthly_rent'];
        
        requiredFields.forEach(function(fieldId) {
            const field = document.getElementById(fieldId);
            if (field && !field.value.trim()) {
                hasErrors = true;
                field.classList.add('is-invalid');
            } else if (field) {
                field.classList.remove('is-invalid');
            }
        });

        if (hasErrors) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});
</script>
{% endblock %}