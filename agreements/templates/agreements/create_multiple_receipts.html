{% extends 'agreements/base.html' %}
{% load static %}

{% block title %}Create Multiple Receipts - House Agreement Generator{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-file-earmark-plus"></i> Create Multiple Rent Receipts
                    </h4>
                    <small>Fill out information for different tenants to generate individual receipts in one document</small>
                </div>
                <div class="card-body">
                    <!-- Display Fixed Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-success border-bottom pb-2 mb-3">
                                <i class="bi bi-info-circle"></i> Fixed Property Information
                            </h5>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <strong>Holding No:</strong> 01663
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <strong>Owner Name:</strong> Md Abdul Mazed
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <strong>Village/Area:</strong> Dangisara, Badalgachhi
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="post" id="multipleReceiptsForm">
                        {% csrf_token %}
                        
                        <!-- File Format Selection -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="bi bi-gear"></i> Document Format
                                </h5>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.file_format.id_for_label }}" class="form-label">{{ form.file_format.label }} *</label>
                                {{ form.file_format }}
                                <small class="text-muted">Choose between PDF or Word document format</small>
                            </div>
                        </div>

                        <!-- Receipt Sections Container -->
                        <div id="receipts-container">
                            <!-- Receipt 1 (Always visible) -->
                            <div class="receipt-section" id="receipt_1">
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h5 class="text-info border-bottom pb-2 mb-3">
                                            <i class="bi bi-receipt"></i> Receipt 1 - Tenant Information
                                            <span class="badge bg-primary">Required</span>
                                        </h5>
                                    </div>
                                    
                                    <!-- Property Type and Unit Selection -->
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.property_type_1.id_for_label }}" class="form-label">Property Type *</label>
                                        {{ form.property_type_1 }}
                                    </div>
                                    <div class="col-md-6 mb-3" id="unit_container_1" style="display: none;">
                                        <label for="unit_select_1" class="form-label">Select Unit</label>
                                        <select id="unit_select_1" class="form-select unit-select" data-receipt="1">
                                            <option value="">Select Unit</option>
                                        </select>
                                        <small class="text-muted">Choose a unit to auto-fill tenant information</small>
                                    </div>
                                    
                                    <!-- Tenant Information -->
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.tenant_name_1.id_for_label }}" class="form-label">Tenant Name *</label>
                                        {{ form.tenant_name_1 }}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.father_name_1.id_for_label }}" class="form-label">Father's Name *</label>
                                        {{ form.father_name_1 }}
                                    </div>
                                    
                                    <!-- Financial Information -->
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.monthly_rent_1.id_for_label }}" class="form-label">Monthly Rent (Taka) *</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.monthly_rent_1 }}
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.electricity_bill_1.id_for_label }}" class="form-label">Electricity Bill (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.electricity_bill_1 }}
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.water_bill_1.id_for_label }}" class="form-label">Water Bill (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.water_bill_1 }}
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.gas_bill_1.id_for_label }}" class="form-label">Gas Bill (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.gas_bill_1 }}
                                        </div>
                                    </div>
                                    
                                    <!-- Date -->
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.agreement_date_1.id_for_label }}" class="form-label">Date *</label>
                                        {{ form.agreement_date_1 }}
                                    </div>
                                    
                                    <!-- Total Display -->
                                    <div class="col-md-6 mb-3">
                                        <div class="alert alert-success">
                                            <strong>Total: ৳<span id="total_1">0.00</span></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Receipt 2 (Hidden by default) -->
                            <div class="receipt-section" id="receipt_2" style="display: none;">
                                <div class="row mb-4">
                                    <div class="col-12 d-flex justify-content-between align-items-center">
                                        <h5 class="text-info border-bottom pb-2 mb-3">
                                            <i class="bi bi-receipt"></i> Receipt 2 - Tenant Information
                                            <span class="badge bg-secondary">Optional</span>
                                        </h5>
                                        <button type="button" class="btn btn-sm btn-outline-danger remove-receipt" data-receipt="2">
                                            <i class="bi bi-trash"></i> Remove
                                        </button>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.property_type_2.id_for_label }}" class="form-label">Property Type</label>
                                        {{ form.property_type_2 }}
                                    </div>
                                    <div class="col-md-6 mb-3" id="unit_container_2" style="display: none;">
                                        <label for="unit_select_2" class="form-label">Select Unit</label>
                                        <select id="unit_select_2" class="form-select unit-select" data-receipt="2">
                                            <option value="">Select Unit</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.tenant_name_2.id_for_label }}" class="form-label">Tenant Name</label>
                                        {{ form.tenant_name_2 }}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.father_name_2.id_for_label }}" class="form-label">Father's Name</label>
                                        {{ form.father_name_2 }}
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.monthly_rent_2.id_for_label }}" class="form-label">Monthly Rent (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.monthly_rent_2 }}
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.electricity_bill_2.id_for_label }}" class="form-label">Electricity Bill (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.electricity_bill_2 }}
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.water_bill_2.id_for_label }}" class="form-label">Water Bill (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.water_bill_2 }}
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.gas_bill_2.id_for_label }}" class="form-label">Gas Bill (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.gas_bill_2 }}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.agreement_date_2.id_for_label }}" class="form-label">Date</label>
                                        {{ form.agreement_date_2 }}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="alert alert-success">
                                            <strong>Total: ৳<span id="total_2">0.00</span></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Receipt 3 (Hidden by default) -->
                            <div class="receipt-section" id="receipt_3" style="display: none;">
                                <div class="row mb-4">
                                    <div class="col-12 d-flex justify-content-between align-items-center">
                                        <h5 class="text-info border-bottom pb-2 mb-3">
                                            <i class="bi bi-receipt"></i> Receipt 3 - Tenant Information
                                            <span class="badge bg-secondary">Optional</span>
                                        </h5>
                                        <button type="button" class="btn btn-sm btn-outline-danger remove-receipt" data-receipt="3">
                                            <i class="bi bi-trash"></i> Remove
                                        </button>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.property_type_3.id_for_label }}" class="form-label">Property Type</label>
                                        {{ form.property_type_3 }}
                                    </div>
                                    <div class="col-md-6 mb-3" id="unit_container_3" style="display: none;">
                                        <label for="unit_select_3" class="form-label">Select Unit</label>
                                        <select id="unit_select_3" class="form-select unit-select" data-receipt="3">
                                            <option value="">Select Unit</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.tenant_name_3.id_for_label }}" class="form-label">Tenant Name</label>
                                        {{ form.tenant_name_3 }}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.father_name_3.id_for_label }}" class="form-label">Father's Name</label>
                                        {{ form.father_name_3 }}
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.monthly_rent_3.id_for_label }}" class="form-label">Monthly Rent (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.monthly_rent_3 }}
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.electricity_bill_3.id_for_label }}" class="form-label">Electricity Bill (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.electricity_bill_3 }}
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.water_bill_3.id_for_label }}" class="form-label">Water Bill (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.water_bill_3 }}
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.gas_bill_3.id_for_label }}" class="form-label">Gas Bill (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.gas_bill_3 }}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.agreement_date_3.id_for_label }}" class="form-label">Date</label>
                                        {{ form.agreement_date_3 }}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="alert alert-success">
                                            <strong>Total: ৳<span id="total_3">0.00</span></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Receipt 4 (Hidden by default) -->
                            <div class="receipt-section" id="receipt_4" style="display: none;">
                                <div class="row mb-4">
                                    <div class="col-12 d-flex justify-content-between align-items-center">
                                        <h5 class="text-info border-bottom pb-2 mb-3">
                                            <i class="bi bi-receipt"></i> Receipt 4 - Tenant Information
                                            <span class="badge bg-secondary">Optional</span>
                                        </h5>
                                        <button type="button" class="btn btn-sm btn-outline-danger remove-receipt" data-receipt="4">
                                            <i class="bi bi-trash"></i> Remove
                                        </button>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.property_type_4.id_for_label }}" class="form-label">Property Type</label>
                                        {{ form.property_type_4 }}
                                    </div>
                                    <div class="col-md-6 mb-3" id="unit_container_4" style="display: none;">
                                        <label for="unit_select_4" class="form-label">Select Unit</label>
                                        <select id="unit_select_4" class="form-select unit-select" data-receipt="4">
                                            <option value="">Select Unit</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.tenant_name_4.id_for_label }}" class="form-label">Tenant Name</label>
                                        {{ form.tenant_name_4 }}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.father_name_4.id_for_label }}" class="form-label">Father's Name</label>
                                        {{ form.father_name_4 }}
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.monthly_rent_4.id_for_label }}" class="form-label">Monthly Rent (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.monthly_rent_4 }}
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.electricity_bill_4.id_for_label }}" class="form-label">Electricity Bill (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.electricity_bill_4 }}
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.water_bill_4.id_for_label }}" class="form-label">Water Bill (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.water_bill_4 }}
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="{{ form.gas_bill_4.id_for_label }}" class="form-label">Gas Bill (Taka)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            {{ form.gas_bill_4 }}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.agreement_date_4.id_for_label }}" class="form-label">Date</label>
                                        {{ form.agreement_date_4 }}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="alert alert-success">
                                            <strong>Total: ৳<span id="total_4">0.00</span></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Add New Receipt Button -->
                        <div class="row mb-4" id="add-receipt-section">
                            <div class="col-12 text-center">
                                <button type="button" id="add-receipt-btn" class="btn btn-outline-success btn-lg">
                                    <i class="bi bi-plus-circle"></i> Add New Receipt
                                </button>
                                <small class="d-block text-muted mt-2">You can add up to 4 receipts total</small>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'home' %}" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-left"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="bi bi-download"></i> Generate Multiple Receipts
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/tenant_selector.js' %}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let activeReceiptCount = 1; // Start with Receipt 1 visible
    const maxReceipts = 4;
    
    const addReceiptBtn = document.getElementById('add-receipt-btn');
    const addReceiptSection = document.getElementById('add-receipt-section');
    
    // Function to show next receipt
    function addNewReceipt() {
        if (activeReceiptCount < maxReceipts) {
            activeReceiptCount++;
            const receiptSection = document.getElementById(`receipt_${activeReceiptCount}`);
            receiptSection.style.display = 'block';
            
            // Set today's date as default for new receipt
            const dateField = document.getElementById(`id_agreement_date_${activeReceiptCount}`);
            if (dateField && !dateField.value) {
                const today = new Date().toISOString().split('T')[0];
                dateField.value = today;
            }
            
            // Hide add button if maximum reached
            if (activeReceiptCount >= maxReceipts) {
                addReceiptSection.style.display = 'none';
            }
            
            // Scroll to new receipt
            receiptSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
    
    // Function to remove receipt
    function removeReceipt(receiptNum) {
        if (receiptNum > 1) { // Can't remove Receipt 1
            const receiptSection = document.getElementById(`receipt_${receiptNum}`);
            receiptSection.style.display = 'none';
            
            // Clear all fields in removed receipt
            clearReceiptData(receiptNum);
            
            // Update active count
            activeReceiptCount = Math.max(1, activeReceiptCount - 1);
            
            // Show add button if we're below maximum
            if (activeReceiptCount < maxReceipts) {
                addReceiptSection.style.display = 'block';
            }
        }
    }
    
    function clearReceiptData(receiptNum) {
        const fields = ['property_type', 'tenant_name', 'father_name', 'monthly_rent', 'electricity_bill', 'water_bill', 'gas_bill', 'agreement_date'];
        fields.forEach(field => {
            const element = document.getElementById(`id_${field}_${receiptNum}`);
            if (element) {
                element.value = '';
            }
        });
        updateTotal(receiptNum);
    }
    
    // Calculate total for each receipt
    function updateTotal(receiptNum) {
        const rent = parseFloat(document.getElementById(`id_monthly_rent_${receiptNum}`).value) || 0;
        const electricity = parseFloat(document.getElementById(`id_electricity_bill_${receiptNum}`).value) || 0;
        const water = parseFloat(document.getElementById(`id_water_bill_${receiptNum}`).value) || 0;
        const gas = parseFloat(document.getElementById(`id_gas_bill_${receiptNum}`).value) || 0;
        
        const total = rent + electricity + water + gas;
        const totalElement = document.getElementById(`total_${receiptNum}`);
        if (totalElement) {
            totalElement.textContent = total.toFixed(2);
        }
    }
    
    // Handle property type changes for unit selection
    function handlePropertyTypeChange(receiptNum) {
        const propertyType = document.getElementById(`id_property_type_${receiptNum}`).value;
        const unitContainer = document.getElementById(`unit_container_${receiptNum}`);
        
        if (propertyType === 'Flat' || propertyType === 'Shop') {
            unitContainer.style.display = 'block';
            populateUnitOptions(propertyType, receiptNum);
        } else {
            unitContainer.style.display = 'none';
        }
    }
    
    function populateUnitOptions(propertyType, receiptNum) {
        const unitSelect = document.getElementById(`unit_select_${receiptNum}`);
        unitSelect.innerHTML = '<option value="">Select Unit</option>';
        
        const tenantData = {
            "flats": [
                {"flat_number": "3A", "tenant_name": "Md. Akhlas Ur Rahman", "father_name": "Md. Abu Masud", "monthly_rent": 25000},
                {"flat_number": "3B", "tenant_name": "SAYED", "father_name": "Shahidul", "monthly_rent": 25000},
                {"flat_number": "4A", "tenant_name": "MD. MEHEDI HASAN", "father_name": "Md. Nasir Uddin", "monthly_rent": 25000},
                {"flat_number": "4B", "tenant_name": "MD. NAZMUS SAKIB SAYCOT", "father_name": "Md. Sadekul Islam", "monthly_rent": 25000}
            ],
            "shops": [
                {"shop_number": "01", "tenant_name": "Md. Jahangir Kabir", "father_name": "Md. Afsar Ali Mandal", "monthly_rent": 35000},
                {"shop_number": "02", "tenant_name": "Shamol Kumar Mondal", "father_name": "Nogendra Nath Mondal", "monthly_rent": 35000}
            ]
        };
        
        if (propertyType === 'Flat') {
            tenantData.flats.forEach(flat => {
                const option = document.createElement('option');
                option.value = JSON.stringify(flat);
                option.textContent = `Flat ${flat.flat_number} - ${flat.tenant_name} (৳${flat.monthly_rent})`;
                unitSelect.appendChild(option);
            });
        } else if (propertyType === 'Shop') {
            tenantData.shops.forEach(shop => {
                const option = document.createElement('option');
                option.value = JSON.stringify(shop);
                option.textContent = `Shop ${shop.shop_number} - ${shop.tenant_name} (৳${shop.monthly_rent})`;
                unitSelect.appendChild(option);
            });
        }
    }
    
    function populateTenantInfo(unitData, receiptNum) {
        if (unitData) {
            const tenant = JSON.parse(unitData);
            document.getElementById(`id_tenant_name_${receiptNum}`).value = tenant.tenant_name;
            document.getElementById(`id_father_name_${receiptNum}`).value = tenant.father_name;
            document.getElementById(`id_monthly_rent_${receiptNum}`).value = tenant.monthly_rent;
            updateTotal(receiptNum);
        }
    }
    
    // Event listeners
    addReceiptBtn.addEventListener('click', addNewReceipt);
    
    // Add event listeners for remove buttons
    document.querySelectorAll('.remove-receipt').forEach(button => {
        button.addEventListener('click', function() {
            const receiptNum = parseInt(this.getAttribute('data-receipt'));
            removeReceipt(receiptNum);
        });
    });
    
    // Add event listeners for each receipt section
    for (let i = 1; i <= 4; i++) {
        // Property type change
        const propertyTypeField = document.getElementById(`id_property_type_${i}`);
        if (propertyTypeField) {
            propertyTypeField.addEventListener('change', function() {
                handlePropertyTypeChange(i);
            });
        }
        
        // Unit selection change
        const unitSelect = document.getElementById(`unit_select_${i}`);
        if (unitSelect) {
            unitSelect.addEventListener('change', function() {
                populateTenantInfo(this.value, i);
            });
        }
        
        // Financial field changes
        ['monthly_rent', 'electricity_bill', 'water_bill', 'gas_bill'].forEach(field => {
            const element = document.getElementById(`id_${field}_${i}`);
            if (element) {
                element.addEventListener('input', function() {
                    updateTotal(i);
                });
            }
        });
        
        // Set today's date as default for Receipt 1
        if (i === 1) {
            const dateField = document.getElementById(`id_agreement_date_${i}`);
            if (dateField && !dateField.value) {
                const today = new Date().toISOString().split('T')[0];
                dateField.value = today;
            }
        }
    }
});
</script>
{% endblock %}