{% extends 'agreements/base.html' %}

{% block title %}All Agreements - House Agreement Generator{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <!-- Header with Actions -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="bi bi-list-ul"></i> All House Agreements
                </h2>
                <a href="{% url 'create_agreement' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Create New Agreement
                </a>
            </div>

            {% if agreements %}
                <!-- Agreements Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Agreement #</th>
                                        <th>Tenant Name</th>
                                        <th>Property Type</th>
                                        <th>Owner Name</th>
                                        <th>Monthly Rent</th>
                                        <th>Total Amount</th>
                                        <th>Agreement Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for agreement in agreements %}
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary">HA-{{ agreement.id|stringformat:"04d" }}</span>
                                        </td>
                                        <td>
                                            <strong>{{ agreement.tenant_name }}</strong>
                                            <br>
                                            <small class="text-muted">S/O: {{ agreement.father_name }}</small>
                                        </td>
                                        <td>{{ agreement.property_type }}</td>
                                        <td>{{ agreement.owner_name }}</td>
                                        <td>
                                            <span class="text-success fw-bold">₹{{ agreement.monthly_rent }}</span>
                                        </td>
                                        <td>
                                            <span class="text-primary fw-bold">₹{{ agreement.total_amount }}</span>
                                        </td>
                                        <td>{{ agreement.agreement_date|date:"M d, Y" }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'agreement_detail' agreement.pk %}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{% url 'generate_pdf' agreement.pk %}" 
                                                   class="btn btn-sm btn-success" 
                                                   title="Download PDF">
                                                    <i class="bi bi-download"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Summary Statistics -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">
                                    <i class="bi bi-file-earmark-text"></i>
                                    Total Agreements
                                </h5>
                                <h3 class="text-primary">{{ agreements|length }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">
                                    <i class="bi bi-currency-rupee"></i>
                                    Average Rent
                                </h5>
                                <h3 class="text-success">
                                    ₹{% if agreements %}{{ agreements|length|add:0 }}{% else %}0{% endif %}
                                </h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info">
                                    <i class="bi bi-calendar"></i>
                                    Latest Agreement
                                </h5>
                                <h6 class="text-info">
                                    {% if agreements %}{{ agreements.first.agreement_date|date:"M d, Y" }}{% else %}None{% endif %}
                                </h6>
                            </div>
                        </div>
                    </div>
                </div>

            {% else %}
                <!-- Empty State -->
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="bi bi-file-earmark-x" style="font-size: 4rem; color: #6c757d;"></i>
                    </div>
                    <h4 class="text-muted mb-3">No Agreements Found</h4>
                    <p class="text-muted mb-4">You haven't created any house agreements yet. Get started by creating your first agreement!</p>
                    <a href="{% url 'create_agreement' %}" class="btn btn-primary btn-lg">
                        <i class="bi bi-plus-circle"></i> Create Your First Agreement
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add confirmation for PDF download
    const pdfButtons = document.querySelectorAll('a[href*="generate-pdf"]');
    pdfButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            // Optional: Add loading state
            const icon = this.querySelector('i');
            const originalClass = icon.className;
            icon.className = 'bi bi-hourglass-split';
            
            // Restore icon after a short delay (PDF generation is usually quick)
            setTimeout(function() {
                icon.className = originalClass;
            }, 2000);
        });
    });

    // Add hover effects to table rows
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(function(row) {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});
</script>
{% endblock %}