from django.db import models
from django.utils import timezone

# Create your models here.
class HouseAgreement(models.Model):
    # Property Details
    property_type = models.CharField(max_length=100, help_text="House/Shop/Flat")
    holding_no = models.CharField(max_length=100, blank=True)
    owner_name = models.CharField(max_length=200)
    village_ward = models.CharField(max_length=200)
    
    # Tenant Details
    tenant_name = models.CharField(max_length=200)
    father_name = models.CharField(max_length=200)
    
    # Financial Details
    monthly_rent = models.DecimalField(max_digits=10, decimal_places=2)
    electricity_bill = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    water_bill = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    gas_bill = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Agreement Details
    duration = models.CharField(max_length=100, default="1 year")
    agreement_date = models.DateField(default=timezone.now)
    
    # Meta
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Agreement: {self.tenant_name} - {self.property_type}"
    
    @property
    def total_amount(self):
        return self.monthly_rent + self.electricity_bill + self.water_bill + self.gas_bill
