from django.db import models
from django.utils import timezone

# Create your models here.
class HouseAgreement(models.Model):
    PROPERTY_TYPE_CHOICES = [
        ('House', 'House'),
        ('Shop', 'Shop'),
        ('Flat', 'Flat'),
    ]
    
    # Property Details (Fixed values)
    property_type = models.CharField(max_length=100, choices=PROPERTY_TYPE_CHOICES)
    holding_no = models.CharField(max_length=100, default='01663', editable=False)
    owner_name = models.CharField(max_length=200, default='Md Abdul Mazed', editable=False)
    village_area = models.CharField(max_length=200, default='Dangisara, Badalgachhi', editable=False)
    
    # Tenant Details
    tenant_name = models.Char<PERSON>ield(max_length=200)
    father_name = models.Char<PERSON>ield(max_length=200)
    
    # Financial Details (in Taka)
    monthly_rent = models.DecimalField(max_digits=10, decimal_places=2, help_text="Monthly rent in Taka")
    electricity_bill = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Electricity bill in Taka")
    water_bill = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Water bill in Taka")
    gas_bill = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Gas bill in Taka")
    
    # Agreement Details
    agreement_date = models.DateField(default=timezone.now)
    receipt_no = models.CharField(max_length=50, blank=True)
    
    # Meta
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def save(self, *args, **kwargs):
        if not self.receipt_no:
            # Generate receipt number after saving to get ID
            super().save(*args, **kwargs)
            self.receipt_no = f"RR-{self.id:04d}"
            super().save(update_fields=['receipt_no'])
        else:
            super().save(*args, **kwargs)
    
    def __str__(self):
        return f"Agreement: {self.tenant_name} - {self.property_type}"
    
    @property
    def total_amount(self):
        return self.monthly_rent + self.electricity_bill + self.water_bill + self.gas_bill
