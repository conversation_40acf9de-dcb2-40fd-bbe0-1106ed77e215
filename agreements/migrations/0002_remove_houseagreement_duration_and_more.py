# Generated by Django 5.2.4 on 2025-07-12 06:06

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("agreements", "0001_initial"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="houseagreement",
            name="duration",
        ),
        migrations.RemoveField(
            model_name="houseagreement",
            name="village_mahalla",
        ),
        migrations.AddField(
            model_name="houseagreement",
            name="duration_years",
            field=models.CharField(
                default="20 years", help_text="Agreement duration", max_length=100
            ),
        ),
        migrations.AddField(
            model_name="houseagreement",
            name="receipt_no",
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name="houseagreement",
            name="village_area",
            field=models.CharField(
                default="Dangisara, Badalgachhi", editable=False, max_length=200
            ),
        ),
        migrations.AlterField(
            model_name="houseagreement",
            name="electricity_bill",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Electricity bill in Taka",
                max_digits=10,
            ),
        ),
        migrations.AlterField(
            model_name="houseagreement",
            name="gas_bill",
            field=models.DecimalField(
                decimal_places=2, default=0, help_text="Gas bill in Taka", max_digits=10
            ),
        ),
        migrations.AlterField(
            model_name="houseagreement",
            name="holding_no",
            field=models.CharField(default="01663", editable=False, max_length=100),
        ),
        migrations.AlterField(
            model_name="houseagreement",
            name="monthly_rent",
            field=models.DecimalField(
                decimal_places=2, help_text="Monthly rent in Taka", max_digits=10
            ),
        ),
        migrations.AlterField(
            model_name="houseagreement",
            name="owner_name",
            field=models.CharField(
                default="Md Abdul Mazed", editable=False, max_length=200
            ),
        ),
        migrations.AlterField(
            model_name="houseagreement",
            name="property_type",
            field=models.CharField(
                choices=[("House", "House"), ("Shop", "Shop"), ("Flat", "Flat")],
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="houseagreement",
            name="water_bill",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Water bill in Taka",
                max_digits=10,
            ),
        ),
    ]
