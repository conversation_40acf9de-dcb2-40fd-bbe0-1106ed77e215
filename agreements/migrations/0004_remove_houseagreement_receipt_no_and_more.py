# Generated by Django 5.2.4 on 2025-07-12 08:34

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("agreements", "0003_remove_houseagreement_duration_years"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="houseagreement",
            name="receipt_no",
        ),
        migrations.RemoveField(
            model_name="houseagreement",
            name="village_area",
        ),
        migrations.AddField(
            model_name="houseagreement",
            name="duration",
            field=models.CharField(default="1 year", max_length=100),
        ),
        migrations.AddField(
            model_name="houseagreement",
            name="village_ward",
            field=models.CharField(default=1, max_length=200),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="houseagreement",
            name="electricity_bill",
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.Alter<PERSON>ield(
            model_name="houseagreement",
            name="gas_bill",
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name="houseagreement",
            name="holding_no",
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AlterField(
            model_name="houseagreement",
            name="monthly_rent",
            field=models.DecimalField(decimal_places=2, max_digits=10),
        ),
        migrations.AlterField(
            model_name="houseagreement",
            name="owner_name",
            field=models.CharField(max_length=200),
        ),
        migrations.AlterField(
            model_name="houseagreement",
            name="property_type",
            field=models.CharField(help_text="House/Shop/Flat", max_length=100),
        ),
        migrations.AlterField(
            model_name="houseagreement",
            name="water_bill",
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
    ]
