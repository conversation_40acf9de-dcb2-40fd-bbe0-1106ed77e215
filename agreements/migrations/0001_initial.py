# Generated by Django 5.2.4 on 2025-07-12 05:52

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="HouseAgreement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "property_type",
                    models.CharField(help_text="House/Shop/Flat", max_length=100),
                ),
                ("holding_no", models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=100)),
                ("owner_name", models.Char<PERSON>ield(max_length=200)),
                ("village_mahalla", models.Char<PERSON>ield(max_length=200)),
                ("tenant_name", models.CharField(max_length=200)),
                ("father_name", models.Char<PERSON>ield(max_length=200)),
                ("monthly_rent", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "electricity_bill",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "water_bill",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "gas_bill",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("duration", models.CharField(default="1 year", max_length=100)),
                ("agreement_date", models.DateField(default=django.utils.timezone.now)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
