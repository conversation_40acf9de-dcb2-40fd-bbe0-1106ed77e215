from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT, WD_ALIGN_VERTICAL
from docx.oxml.shared import OxmlElement, qn
import os
from django.conf import settings
from datetime import datetime

class WordReceiptGenerator:
    def __init__(self):
        self.doc = Document()
        self.setup_document()
    
    def setup_document(self):
        """Setup document margins and style for compact layout"""
        sections = self.doc.sections
        for section in sections:
            section.top_margin = Inches(0.3)
            section.bottom_margin = Inches(0.3)
            section.left_margin = Inches(0.3)
            section.right_margin = Inches(0.3)
    
    def add_border_to_table(self, table):
        """Add borders to table"""
        tbl = table._tbl
        tblBorders = OxmlElement('w:tblBorders')
        
        borders = ['top', 'left', 'bottom', 'right', 'insideH', 'insideV']
        for border in borders:
            border_el = OxmlElement(f'w:{border}')
            border_el.set(qn('w:val'), 'single')
            border_el.set(qn('w:sz'), '4')
            border_el.set(qn('w:space'), '0')
            border_el.set(qn('w:color'), '000000')
            tblBorders.append(border_el)
        
        tbl.tblPr.append(tblBorders)
    
    def create_receipt(self, agreement, receipt_date=None):
        """Create a single compact receipt"""
        if receipt_date is None:
            receipt_date = datetime.now().strftime("%B %Y")
        
        # Create a table for the receipt (2x4 layout for compact design)
        table = self.doc.add_table(rows=8, cols=2)
        table.alignment = WD_TABLE_ALIGNMENT.CENTER
        self.add_border_to_table(table)
        
        # Set column widths
        table.columns[0].width = Inches(1.5)
        table.columns[1].width = Inches(2.5)
        
        # Header
        header_cell = table.cell(0, 0)
        header_cell.merge(table.cell(0, 1))
        header_para = header_cell.paragraphs[0]
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        header_run = header_para.add_run("RENT RECEIPT")
        header_run.font.size = Pt(14)
        header_run.font.bold = True
        
        # Receipt details
        details = [
            ("Receipt Date:", datetime.now().strftime("%d/%m/%Y")),
            ("Period:", receipt_date),
            ("Tenant Name:", agreement.tenant_name),
            ("Property:", f"{agreement.property_type}, {agreement.village_ward}"),
            ("Monthly Rent:", f"₹{agreement.monthly_rent}"),
            ("Electricity:", f"₹{agreement.electricity_bill}"),
            ("Total Amount:", f"₹{agreement.total_amount}")
        ]
        
        for i, (label, value) in enumerate(details, 1):
            label_cell = table.cell(i, 0)
            value_cell = table.cell(i, 1)
            
            # Label formatting
            label_para = label_cell.paragraphs[0]
            label_run = label_para.add_run(label)
            label_run.font.size = Pt(9)
            label_run.font.bold = True
            
            # Value formatting
            value_para = value_cell.paragraphs[0]
            value_run = value_para.add_run(value)
            value_run.font.size = Pt(9)
            
            # Set cell height
            label_cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
            value_cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
        
        # Set table height to be compact
        for row in table.rows:
            row.height = Inches(0.25)
    
    def generate_four_receipts_word(self, agreement, months=None):
        """Generate a Word document with 4 receipts on one page"""
        if months is None:
            # Generate for current and next 3 months
            from datetime import datetime, timedelta
            current_date = datetime.now()
            months = []
            for i in range(4):
                month_date = current_date + timedelta(days=30*i)
                months.append(month_date.strftime("%B %Y"))
        
        # Create 4 receipts arranged in 2x2 grid
        for i, month in enumerate(months[:4]):
            self.create_receipt(agreement, month)
            
            # Add page break after every 2 receipts (except the last one)
            if i == 1:
                # Add some space between top and bottom receipts
                self.doc.add_paragraph()
            elif i < 3:
                # Add small space between receipts
                self.doc.add_paragraph()
        
        # Generate filename and save
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"four_receipts_{agreement.tenant_name.replace(' ', '_')}_{timestamp}.docx"
        
        # Ensure media directory exists
        media_dir = os.path.join(settings.BASE_DIR, 'media')
        os.makedirs(media_dir, exist_ok=True)
        
        filepath = os.path.join(media_dir, filename)
        self.doc.save(filepath)
        
        return filename, filepath
    
    def generate_multiple_receipts_word(self, receipt_data_list, filename=None):
        """Generate Word document with multiple receipts - 2 per row, 4 per page maximum"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"multiple_receipts_{timestamp}.docx"

        # Ensure media directory exists
        media_dir = os.path.join(settings.BASE_DIR, 'media')
        os.makedirs(media_dir, exist_ok=True)

        filepath = os.path.join(media_dir, filename)

        # Process receipts in groups of 4 (2 rows of 2 receipts each)
        for page_start in range(0, len(receipt_data_list), 4):
            page_receipts = receipt_data_list[page_start:page_start + 4]

            # Add page break if this is not the first page
            if page_start > 0:
                self.doc.add_page_break()

            # Process receipts in pairs for each row
            for row_start in range(0, len(page_receipts), 2):
                row_receipts = page_receipts[row_start:row_start + 2]

                # Create horizontal layout for this row
                if len(row_receipts) == 2:
                    # Two receipts side by side
                    self._create_receipt_row_word(row_receipts[0], row_receipts[1])
                else:
                    # Single receipt (last one if odd number)
                    self._create_receipt_row_word(row_receipts[0], None)

                # Add space between rows (but not after the last row on page)
                if row_start + 2 < len(page_receipts):
                    self.doc.add_paragraph()

        self.doc.save(filepath)
        return filename, filepath

    def _create_receipt_row_word(self, receipt1_data, receipt2_data=None):
        """Create a row with 1 or 2 receipts side by side in Word"""
        if receipt2_data is None:
            # Single receipt - use full width
            self.create_compact_receipt_word(receipt1_data, full_width=True)
        else:
            # Two receipts side by side using a table layout
            table = self.doc.add_table(rows=1, cols=2)
            table.alignment = WD_TABLE_ALIGNMENT.CENTER

            # Set column widths for side-by-side layout
            table.columns[0].width = Inches(3.5)
            table.columns[1].width = Inches(3.5)

            # Create receipts in each cell
            left_cell = table.cell(0, 0)
            right_cell = table.cell(0, 1)

            # Clear default paragraphs and add receipt content
            left_cell.paragraphs[0].clear()
            right_cell.paragraphs[0].clear()

            # Add receipt content to each cell
            self._add_receipt_to_cell(left_cell, receipt1_data)
            self._add_receipt_to_cell(right_cell, receipt2_data)

    def _add_receipt_to_cell(self, cell, agreement_data):
        """Add a compact receipt to a table cell"""
        # Title
        title_para = cell.add_paragraph()
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_run = title_para.add_run("RENT RECEIPT")
        title_run.font.size = Pt(12)
        title_run.font.bold = True

        # Red header bar (simulated with colored text)
        header_para = cell.add_paragraph()
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        header_run = header_para.add_run("part of the owner")
        header_run.font.size = Pt(9)
        header_run.font.bold = True
        header_run.font.color.rgb = RGBColor(255, 255, 255)  # White text
        # Note: Word doesn't easily support background colors in paragraphs

        # Receipt details in a compact format
        details = [
            ("Date:", agreement_data.agreement_date.strftime("%d/%m/%Y")),
            ("Property:", agreement_data.property_type),
            ("Holding No.:", agreement_data.holding_no),
            ("Owner:", agreement_data.owner_name[:15] + "..." if len(agreement_data.owner_name) > 15 else agreement_data.owner_name),
            ("Village:", getattr(agreement_data, 'village_ward', 'Dangisara, Badalgachhi')[:15] + "..." if len(getattr(agreement_data, 'village_ward', 'Dangisara, Badalgachhi')) > 15 else getattr(agreement_data, 'village_ward', 'Dangisara, Badalgachhi')),
            ("Tenant:", agreement_data.tenant_name[:15] + "..." if len(agreement_data.tenant_name) > 15 else agreement_data.tenant_name),
            ("Father:", agreement_data.father_name[:15] + "..." if len(agreement_data.father_name) > 15 else agreement_data.father_name),
            ("Rent:", f"Tk. {agreement_data.monthly_rent}"),
            ("Electricity:", f"Tk. {agreement_data.electricity_bill}"),
            ("Water:", f"Tk. {agreement_data.water_bill}"),
            ("Gas:", f"Tk. {agreement_data.gas_bill}"),
            ("Total:", f"Tk. {agreement_data.total_amount}"),
        ]

        for label, value in details:
            detail_para = cell.add_paragraph()
            label_run = detail_para.add_run(f"{label} ")
            label_run.font.size = Pt(8)
            label_run.font.bold = True

            value_run = detail_para.add_run(value)
            value_run.font.size = Pt(8)

        # Signature line
        sig_para = cell.add_paragraph()
        sig_para.alignment = WD_ALIGN_PARAGRAPH.LEFT
        sig_run = sig_para.add_run("Signature of tenant:")
        sig_run.font.size = Pt(7)
        sig_run.font.bold = True
        sig_run.font.color.rgb = RGBColor(255, 0, 0)  # Red text

    def create_compact_receipt_word(self, agreement_data, full_width=False):
        """Create a compact receipt in Word format"""
        # Title
        title_para = self.doc.add_paragraph()
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_run = title_para.add_run("RENT RECEIPT")
        title_run.font.size = Pt(14 if full_width else 12)
        title_run.font.bold = True

        # Red header bar (simulated)
        header_para = self.doc.add_paragraph()
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        header_run = header_para.add_run("part of the owner")
        header_run.font.size = Pt(10 if full_width else 9)
        header_run.font.bold = True
        header_run.font.color.rgb = RGBColor(255, 0, 0)  # Red text

        # Receipt details
        details = [
            ("Date:", agreement_data.agreement_date.strftime("%d/%m/%Y")),
            ("House/Shop/Flat:", agreement_data.property_type),
            ("Holding No.:", agreement_data.holding_no),
            ("Owned by:", agreement_data.owner_name),
            ("Village/Mahalla:", getattr(agreement_data, 'village_ward', 'Dangisara, Badalgachhi')),
            ("Tenant Name:", agreement_data.tenant_name),
            ("Father's Name:", agreement_data.father_name),
            ("Monthly Rent:", f"Tk. {agreement_data.monthly_rent}"),
            ("Electricity Bill:", f"Tk. {agreement_data.electricity_bill}"),
            ("Water Bill:", f"Tk. {agreement_data.water_bill}"),
            ("Gas Bill:", f"Tk. {agreement_data.gas_bill}"),
            ("Total Amount:", f"Tk. {agreement_data.total_amount}"),
        ]

        for label, value in details:
            detail_para = self.doc.add_paragraph()
            label_run = detail_para.add_run(f"{label} ")
            label_run.font.size = Pt(10 if full_width else 9)
            label_run.font.bold = True

            value_run = detail_para.add_run(value)
            value_run.font.size = Pt(10 if full_width else 9)

        # Signature section
        sig_para = self.doc.add_paragraph()
        sig_para.alignment = WD_ALIGN_PARAGRAPH.LEFT
        sig_run = sig_para.add_run("Signature of the tenant or person on his behalf")
        sig_run.font.size = Pt(9 if full_width else 8)
        sig_run.font.bold = True
        sig_run.font.color.rgb = RGBColor(255, 0, 0)  # Red text