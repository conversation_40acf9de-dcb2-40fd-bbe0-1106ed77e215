from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from django.conf import settings
import os
from datetime import datetime

class WordDocumentGenerator:
    def __init__(self):
        pass
    
    def create_receipt_table(self, doc, agreement_data):
        """Create a single receipt table for Word document"""
        # Title
        title = doc.add_heading('Rent Receipt', level=1)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Red header
        header_para = doc.add_paragraph('part of the owner')
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        header_run = header_para.runs[0]
        header_run.font.bold = True
        header_run.font.color.rgb = RGBColor(255, 0, 0)
        
        # Create table with 7 columns, 9 rows
        table = doc.add_table(rows=9, cols=7)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        # Fill table data
        table_data = [
            ["No.", "", "", "Date:", f"{agreement_data.agreement_date.strftime('%d/%m/%Y')}", "", ""],
            ["House/Shop/Flat:", agreement_data.property_type, "", "Holding No.", agreement_data.holding_no, "", ""],
            ["Owned by:", agreement_data.owner_name, "", "", "", "", ""],
            ["Village/Mahalla:", agreement_data.village_area, "", "", "", "", ""],
            ["Tenant Name:", agreement_data.tenant_name, "", "", "", "", ""],
            ["Father's Name:", agreement_data.father_name, "", "", "", "", ""],
            ["", "", "Monthly rent:", f"{agreement_data.monthly_rent:.2f}", "Money", "", ""],
            ["Electricity Bill:", f"{agreement_data.electricity_bill:.2f}", "Water bill:", f"{agreement_data.water_bill:.2f}", "Gas bill:", f"{agreement_data.gas_bill:.2f}", ""],
            ["", "", "Total bill:", f"{agreement_data.total_amount:.2f}", "received", "", ""],
        ]
        
        # Populate table
        for i, row_data in enumerate(table_data):
            for j, cell_data in enumerate(row_data):
                if j < len(table.rows[i].cells):
                    table.rows[i].cells[j].text = str(cell_data)
        
        # Merge cells as needed
        self.merge_cells(table, 1, 0, 2, 0)  # No. field
        self.merge_cells(table, 1, 2, 6, 2)  # Owned by
        self.merge_cells(table, 1, 3, 6, 3)  # Village/Mahalla
        self.merge_cells(table, 1, 4, 6, 4)  # Tenant Name
        self.merge_cells(table, 1, 5, 6, 5)  # Father's Name
        self.merge_cells(table, 0, 6, 1, 6)  # Empty before Monthly rent
        self.merge_cells(table, 0, 8, 1, 8)  # Empty before Total
        
        # Add signature section
        doc.add_paragraph()
        signature_para = doc.add_paragraph('Signature of the tenant or person on his behalf')
        signature_run = signature_para.runs[0]
        signature_run.font.color.rgb = RGBColor(255, 0, 0)
        signature_run.font.bold = True
        
        return doc
    
    def merge_cells(self, table, start_col, start_row, end_col, end_row):
        """Helper method to merge table cells"""
        try:
            start_cell = table.rows[start_row].cells[start_col]
            end_cell = table.rows[end_row].cells[end_col]
            start_cell.merge(end_cell)
        except:
            pass  # Skip if merge fails
    
    def generate_word_document(self, agreement_data, receipts_per_page=1, filename=None):
        """Generate Word document with specified number of receipts per page"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"rent_receipt_{agreement_data.tenant_name.replace(' ', '_')}_{timestamp}.docx"
        
        # Ensure media directory exists
        media_dir = settings.MEDIA_ROOT
        os.makedirs(media_dir, exist_ok=True)
        
        filepath = os.path.join(media_dir, filename)
        
        doc = Document()
        
        # Generate the specified number of receipts
        for i in range(int(receipts_per_page)):
            if i > 0:
                doc.add_page_break()
            self.create_receipt_table(doc, agreement_data)
        
        doc.save(filepath)
        return filename, filepath

    def generate_multiple_receipts_document(self, receipt_data_list, filename=None):
        """Generate Word document with multiple different receipts"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"multiple_receipts_{timestamp}.docx"
        
        # Ensure media directory exists
        media_dir = settings.MEDIA_ROOT
        os.makedirs(media_dir, exist_ok=True)
        
        filepath = os.path.join(media_dir, filename)
        
        doc = Document()
        
        # Generate each receipt for different tenants
        for i, agreement_data in enumerate(receipt_data_list):
            if i > 0:
                doc.add_page_break()
            self.create_receipt_table(doc, agreement_data)
        
        doc.save(filepath)
        return filepath