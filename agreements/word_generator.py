from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT, WD_ALIGN_VERTICAL
from docx.oxml.shared import OxmlElement, qn
import os
from django.conf import settings
from datetime import datetime

class WordReceiptGenerator:
    def __init__(self):
        self.doc = Document()
        self.setup_document()
    
    def setup_document(self):
        """Setup document margins and style for compact layout"""
        sections = self.doc.sections
        for section in sections:
            section.top_margin = Inches(0.3)
            section.bottom_margin = Inches(0.3)
            section.left_margin = Inches(0.3)
            section.right_margin = Inches(0.3)
    
    def add_border_to_table(self, table):
        """Add borders to table"""
        tbl = table._tbl
        tblBorders = OxmlElement('w:tblBorders')
        
        borders = ['top', 'left', 'bottom', 'right', 'insideH', 'insideV']
        for border in borders:
            border_el = OxmlElement(f'w:{border}')
            border_el.set(qn('w:val'), 'single')
            border_el.set(qn('w:sz'), '4')
            border_el.set(qn('w:space'), '0')
            border_el.set(qn('w:color'), '000000')
            tblBorders.append(border_el)
        
        tbl.tblPr.append(tblBorders)
    
    def create_receipt(self, agreement, receipt_date=None):
        """Create a single compact receipt"""
        if receipt_date is None:
            receipt_date = datetime.now().strftime("%B %Y")
        
        # Create a table for the receipt (2x4 layout for compact design)
        table = self.doc.add_table(rows=8, cols=2)
        table.alignment = WD_TABLE_ALIGNMENT.CENTER
        self.add_border_to_table(table)
        
        # Set column widths
        table.columns[0].width = Inches(1.5)
        table.columns[1].width = Inches(2.5)
        
        # Header
        header_cell = table.cell(0, 0)
        header_cell.merge(table.cell(0, 1))
        header_para = header_cell.paragraphs[0]
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        header_run = header_para.add_run("RENT RECEIPT")
        header_run.font.size = Pt(14)
        header_run.font.bold = True
        
        # Receipt details
        details = [
            ("Receipt Date:", datetime.now().strftime("%d/%m/%Y")),
            ("Period:", receipt_date),
            ("Tenant Name:", agreement.tenant_name),
            ("Property:", f"{agreement.property_type}, {agreement.village_mahalla}"),
            ("Monthly Rent:", f"₹{agreement.monthly_rent}"),
            ("Electricity:", f"₹{agreement.electricity_bill}"),
            ("Total Amount:", f"₹{agreement.total_amount}")
        ]
        
        for i, (label, value) in enumerate(details, 1):
            label_cell = table.cell(i, 0)
            value_cell = table.cell(i, 1)
            
            # Label formatting
            label_para = label_cell.paragraphs[0]
            label_run = label_para.add_run(label)
            label_run.font.size = Pt(9)
            label_run.font.bold = True
            
            # Value formatting
            value_para = value_cell.paragraphs[0]
            value_run = value_para.add_run(value)
            value_run.font.size = Pt(9)
            
            # Set cell height
            label_cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
            value_cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
        
        # Set table height to be compact
        for row in table.rows:
            row.height = Inches(0.25)
    
    def generate_four_receipts_word(self, agreement, months=None):
        """Generate a Word document with 4 receipts on one page"""
        if months is None:
            # Generate for current and next 3 months
            from datetime import datetime, timedelta
            current_date = datetime.now()
            months = []
            for i in range(4):
                month_date = current_date + timedelta(days=30*i)
                months.append(month_date.strftime("%B %Y"))
        
        # Create 4 receipts arranged in 2x2 grid
        for i, month in enumerate(months[:4]):
            self.create_receipt(agreement, month)
            
            # Add page break after every 2 receipts (except the last one)
            if i == 1:
                # Add some space between top and bottom receipts
                self.doc.add_paragraph()
            elif i < 3:
                # Add small space between receipts
                self.doc.add_paragraph()
        
        # Generate filename and save
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"four_receipts_{agreement.tenant_name.replace(' ', '_')}_{timestamp}.docx"
        
        # Ensure media directory exists
        media_dir = os.path.join(settings.BASE_DIR, 'media')
        os.makedirs(media_dir, exist_ok=True)
        
        filepath = os.path.join(media_dir, filename)
        self.doc.save(filepath)
        
        return filename, filepath
    
    def generate_multiple_receipts_word(self, agreements, months=None):
        """Generate Word document with multiple receipts for different tenants"""
        if months is None:
            from datetime import datetime
            current_date = datetime.now()
            months = [current_date.strftime("%B %Y")]
        
        for i, agreement in enumerate(agreements):
            for month in months:
                self.create_receipt(agreement, month)
                
                # Add space between receipts
                self.doc.add_paragraph()
            
            # Add page break between different tenants (except last)
            if i < len(agreements) - 1:
                self.doc.add_page_break()
        
        # Generate filename and save
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"multiple_receipts_{timestamp}.docx"
        
        # Ensure media directory exists
        media_dir = os.path.join(settings.BASE_DIR, 'media')
        os.makedirs(media_dir, exist_ok=True)
        
        filepath = os.path.join(media_dir, filename)
        self.doc.save(filepath)
        
        return filename, filepath