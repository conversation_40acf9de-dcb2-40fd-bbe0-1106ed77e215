// Tenant data for automatic population
const tenantData = {
    "flats": [
        {
            "flat_number": "3A",
            "tenant_name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>",
            "father_name": "<PERSON><PERSON><PERSON>",
            "monthly_rent": 25000
        },
        {
            "flat_number": "3B",
            "tenant_name": "SAYED",
            "father_name": "<PERSON><PERSON><PERSON>",
            "monthly_rent": 25000
        },
        {
            "flat_number": "4A",
            "tenant_name": "MD. MEHEDI HASAN",
            "father_name": "Md. <PERSON>",
            "monthly_rent": 25000
        },
        {
            "flat_number": "4B",
            "tenant_name": "MD. NAZMUS SAKIB SAYCOT",
            "father_name": "<PERSON>d. <PERSON>ek<PERSON>",
            "monthly_rent": 25000
        }
    ],
    "shops": [
        {
            "shop_number": "01",
            "tenant_name": "Md. <PERSON><PERSON><PERSON><PERSON>",
            "father_name": "Md. Afsar <PERSON>",
            "monthly_rent": 35000
        },
        {
            "shop_number": "02",
            "tenant_name": "<PERSON><PERSON><PERSON>",
            "father_name": "<PERSON><PERSON><PERSON>",
            "monthly_rent": 35000
        }
    ]
};

// Function to populate unit options based on property type
function populateUnitOptions(propertyType) {
    const unitSelect = document.getElementById('unit_select');
    const tenantNameField = document.getElementById('id_tenant_name');
    const fatherNameField = document.getElementById('id_father_name');
    const monthlyRentField = document.getElementById('id_monthly_rent');
    
    // Clear existing options
    unitSelect.innerHTML = '<option value="">Select Unit</option>';
    
    // Clear tenant fields
    tenantNameField.value = '';
    fatherNameField.value = '';
    monthlyRentField.value = '';
    
    if (propertyType === 'Flat') {
        tenantData.flats.forEach(flat => {
            const option = document.createElement('option');
            option.value = JSON.stringify(flat);
            option.textContent = `Flat ${flat.flat_number} - ${flat.tenant_name} (৳${flat.monthly_rent})`;
            unitSelect.appendChild(option);
        });
    } else if (propertyType === 'Shop') {
        tenantData.shops.forEach(shop => {
            const option = document.createElement('option');
            option.value = JSON.stringify(shop);
            option.textContent = `Shop ${shop.shop_number} - ${shop.tenant_name} (৳${shop.monthly_rent})`;
            unitSelect.appendChild(option);
        });
    }
    
    // Show/hide the unit selector
    const unitContainer = document.getElementById('unit_container');
    if (propertyType === 'Flat' || propertyType === 'Shop') {
        unitContainer.style.display = 'block';
    } else {
        unitContainer.style.display = 'none';
    }
}

// Function to populate tenant information when unit is selected
function populateTenantInfo(unitData) {
    if (unitData) {
        const tenant = JSON.parse(unitData);
        document.getElementById('id_tenant_name').value = tenant.tenant_name;
        document.getElementById('id_father_name').value = tenant.father_name;
        document.getElementById('id_monthly_rent').value = tenant.monthly_rent;
        
        // Trigger the total calculation after populating rent
        const event = new Event('input', { bubbles: true });
        document.getElementById('id_monthly_rent').dispatchEvent(event);
    }
}

// Initialize the form when page loads
document.addEventListener('DOMContentLoaded', function() {
    const propertyTypeField = document.getElementById('id_property_type');
    const unitSelect = document.getElementById('unit_select');
    
    // Listen for property type changes
    propertyTypeField.addEventListener('change', function() {
        populateUnitOptions(this.value);
    });
    
    // Listen for unit selection changes
    unitSelect.addEventListener('change', function() {
        populateTenantInfo(this.value);
    });
    
    // Initialize on page load
    populateUnitOptions(propertyTypeField.value);
});