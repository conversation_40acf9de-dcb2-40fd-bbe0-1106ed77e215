<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

# House Agreement Generator - Django Application

This is a Django web application for generating professional house rental agreement PDFs.

## Project Structure
- **Django Framework**: Web application with forms, models, and views
- **ReportLab**: PDF generation for house agreements
- **Bootstrap 5**: Modern, responsive UI styling
- **SQLite**: Database for storing agreement data

## Key Components
- **Models**: HouseAgreement model with property, tenant, and financial details
- **Forms**: Django ModelForm with Bootstrap styling for data input
- **Views**: CRUD operations and PDF generation functionality
- **Templates**: Professional HTML templates with CSS styling
- **PDF Generator**: Custom ReportLab-based PDF creation with tables and formatting

## Features
- Responsive web form for agreement data entry
- Real-time total amount calculation
- Professional PDF generation with legal formatting
- Agreement management (create, view, list)
- Bootstrap-styled interface with modern design

## Development Notes
- Uses Django's built-in SQLite database
- PDF files are stored in media/ directory
- Static files served during development
- Form validation and error handling included
- Mobile-responsive design with Bootstrap